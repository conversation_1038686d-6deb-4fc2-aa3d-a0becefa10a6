package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"ai-codereview-service/internal/config"
	"ai-codereview-service/internal/handler"
	"ai-codereview-service/internal/llm"
	"ai-codereview-service/internal/notification"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/internal/service"
	"ai-codereview-service/pkg/logger"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志（使用配置文件中的设置）
	logger.InitWithConfig(cfg.Logging.Level, cfg.Logging.File)
	logger.Info("Starting AI Code Review GitLab service...")

	// 验证必要的配置
	if err := validateConfig(cfg); err != nil {
		log.Fatalf("Config validation failed: %v", err)
	}

	// 初始化数据库
	repo, err := repository.New(cfg.Database.Type, cfg.Database.DSN)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 初始化LLM客户端
	llmClient := llm.NewClient(
		cfg.LLM.APIKey,
		cfg.LLM.BaseURL,
		cfg.LLM.Model,
		cfg.LLM.MaxTokens,
	)

	// 初始化通知服务
	notifier := notification.NewDingTalkNotifier(
		cfg.DingTalk.WebhookURL,
		cfg.DingTalk.Enabled,
	)

	// 初始化业务服务（不再需要固定的GitLab客户端）
	reviewService := service.NewReviewService(
		cfg,
		repo,
		llmClient,
		notifier,
	)

	// 初始化HTTP处理器
	webhookHandler := handler.NewWebhookHandler(reviewService)
	dingTalkConfigHandler := handler.NewDingTalkConfigHandler(repo)

	// 设置Gin模式
	if cfg.Server.Host == "0.0.0.0" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(gin.Recovery())
	router.Use(gin.Logger())

	// 统一API路径到 /api/v1
	api := router.Group("/api/v1")
	{
		// GitLab webhook (保持原路径以兼容现有GitLab配置)
		api.POST("/webhook", webhookHandler.HandleGitLabWebhook)

		// 审查相关API
		review := api.Group("/review")
		{
			review.POST("/manual", webhookHandler.HandleManualReview)
		}

		// 钉钉通知配置管理API
		dingtalk := api.Group("/dingtalk-configs")
		{
			dingtalk.POST("", dingTalkConfigHandler.CreateDingTalkConfig)
			dingtalk.GET("", dingTalkConfigHandler.GetDingTalkConfigs)
			dingtalk.GET("/:id", dingTalkConfigHandler.GetDingTalkConfig)
			dingtalk.PUT("/:id", dingTalkConfigHandler.UpdateDingTalkConfig)
			dingtalk.DELETE("/:id", dingTalkConfigHandler.DeleteDingTalkConfig)
		}

		// 系统状态API
		system := api.Group("/system")
		{
			system.GET("/health", webhookHandler.HealthCheck)
			system.GET("/stats", webhookHandler.GetStats)
		}
	}

	// 启动服务器
	addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	logger.Infof("Server starting on %s", addr)

	// 优雅关闭
	go func() {
		if err := router.Run(addr); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	logger.Infof("Server started successfully on %s", addr)
	logger.Info("Press Ctrl+C to stop the server")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down...")
}

func validateConfig(cfg *config.Config) error {
	if cfg.GitLab.URL == "" && os.Getenv("GITLAB_URL") == "" {
		return fmt.Errorf("gitlab.url is required in config file or GITLAB_URL environment variable")
	}
	if cfg.GitLab.Token == "" && os.Getenv("GITLAB_ACCESS_TOKEN") == "" {
		return fmt.Errorf("gitlab.token is required in config file or GITLAB_ACCESS_TOKEN environment variable")
	}
	if cfg.LLM.APIKey == "" {
		return fmt.Errorf("llm.api_key is required in config file")
	}
	if len(cfg.GitLab.TargetBranches) == 0 {
		return fmt.Errorf("gitlab.target_branches is required in config file")
	}

	logger.Infof("Configuration validated successfully")
	logger.Infof("GitLab URL: %s", getGitLabURL(cfg))
	logger.Infof("GitLab Token: %s", maskToken(getGitLabToken(cfg)))
	logger.Infof("Target branches: %v", cfg.GitLab.TargetBranches)
	logger.Infof("Review enabled: %v", cfg.Review.Enabled)
	logger.Infof("DingTalk enabled: %v", cfg.DingTalk.Enabled)
	logger.Infof("LLM Model: %s", cfg.LLM.Model)
	logger.Infof("Log level: %s", cfg.Logging.Level)

	return nil
}

func getGitLabURL(cfg *config.Config) string {
	if cfg.GitLab.URL != "" {
		return cfg.GitLab.URL
	}
	return os.Getenv("GITLAB_URL")
}

func getGitLabToken(cfg *config.Config) string {
	if cfg.GitLab.Token != "" {
		return cfg.GitLab.Token
	}
	return os.Getenv("GITLAB_ACCESS_TOKEN")
}

// maskToken 脱敏显示token
func maskToken(token string) string {
	if token == "" {
		return "<not set>"
	}
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "****" + token[len(token)-4:]
}
