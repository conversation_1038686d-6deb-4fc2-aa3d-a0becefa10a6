package logger

import (
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
)

var log *logrus.Logger

func Init() *logrus.Logger {
	log = logrus.New()

	// 设置日志格式
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 设置默认日志级别
	log.SetLevel(logrus.InfoLevel)

	return log
}

func InitWithConfig(level, logFile string) *logrus.Logger {
	log = logrus.New()

	// 设置日志格式
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 设置日志级别
	switch strings.ToLower(level) {
	case "debug":
		log.SetLevel(logrus.DebugLevel)
	case "info":
		log.SetLevel(logrus.InfoLevel)
	case "warn", "warning":
		log.SetLevel(logrus.WarnLevel)
	case "error":
		log.SetLevel(logrus.ErrorLevel)
	default:
		log.SetLevel(logrus.InfoLevel)
	}

	// 设置日志输出：同时输出到控制台和文件
	var writers []io.Writer
	
	// 添加控制台输出
	writers = append(writers, os.Stdout)
	
	// 如果配置了日志文件，添加文件输出
	if logFile != "" {
		// 创建日志目录
		logDir := filepath.Dir(logFile)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			log.Warnf("Failed to create log directory: %v", err)
		} else {
			// 设置日志文件输出
			file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				log.Warnf("Failed to open log file: %v", err)
			} else {
				writers = append(writers, file)
			}
		}
	}
	
	// 设置多重输出
	multiWriter := io.MultiWriter(writers...)
	log.SetOutput(multiWriter)

	return log
}

func Get() *logrus.Logger {
	if log == nil {
		return Init()
	}
	return log
}

func Info(args ...interface{}) {
	Get().Info(args...)
}

func Infof(format string, args ...interface{}) {
	Get().Infof(format, args...)
}

func Warn(args ...interface{}) {
	Get().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	Get().Warnf(format, args...)
}

func Error(args ...interface{}) {
	Get().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	Get().Errorf(format, args...)
}

func Debug(args ...interface{}) {
	Get().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	Get().Debugf(format, args...)
}
