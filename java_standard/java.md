---
description: 后端架构说明
globs: 
alwaysApply: true
---

后端架构说明

### 后端技术栈说明
1. Dubbo 框架
2. MySQL
3. Mybatis
4. Kafka
5. Redis
6. Aries (公司自研的定时任务/延时任务组件)

### 代码结构
代码库通常包含 api 模块和 core 模块，api 打成 jar 包提供给调用方，core 模块实现 api 逻辑。
#### api 模块
包含对外提供服务的接口类和一些 Req 和 DTO 类。
Req 类和 DTO 类规范：
1. 实现 Serializable 接口
2. 使用 lombok @Data 注解
3. 使用 @NotNull 等常见 javax.validation 注解简化校验代码

#### core 模块
层级/组件调用关系：
```mermaid
graph TD
   AriesListener --> Manager
   Controller --> Manager
   KafkaListener --> Manager
   Manager --> Repository
   Repository --> Mapper
   Manager --> Redis
   Manager --> Rpc
   Manager --> Apollo
   Manager --> KafkaSender
   Manager --> Aries
```







2. 抛异常使用 com.yupaopao.platform.common.exception.YppRunTimeException
错误码类：com.yupaopao.platform.common.dto.Code
**抛 toast 异常，后端抛的 toast 异常会在前端直接展示出弹框:**
```
throw new YppRunTimeException(Code.toast("任务不存在"))
```

3. **代码中已经存在全局的 YppRunTimeException 异常捕获，放心且必须使用 YppRunTimeException，而不是通过 return true/false 标识是否成功**

4. **所有发放奖励的流程都放到最后，保证极端情况下平台不亏损**。比如先插入用户领取记录，再发放奖励；先扣除用户资产，再发放奖励；先结束玩法轮次，再发放奖励。
5. 除了 Redis, 查询逻辑都必须避免 N + 1 问题，包括 SQL, RPC，Manager 调用
6. 不在 for 循环中嵌套查询，容易造成 N + 1 问题，影响性能
7. 线程池使用参考线程池规范
8. 3个及以上的查询，如果允许，必须并行，使用 syncExecutor，参考线程池使用规范和并行查询规范。
9. JSON 相关，使用 fastJson，反序列化泛型的使用必须用 TypeReference
10. tradeNo 使用 `StringUtils.replace(UUID.randomUUID().toString(), "-", "")` 生成
11. 打赏有免费打赏和付费打赏，有些业务只有付费打赏才能触发。
12. Java 项目通常都会引用很多第三方 Jar 包，codebase_search, read_file, grep_search, file_search 都无法读取 jar 包里面的内容，只能通过 get_class_source_code 读取，你可以通过包名判断出该类是否来源于第三方 Jar 包。通常一些公共枚举和 dubbo 依赖需要通过 get_class_source_code 获取。
13. 不要实现 getter/setter，使用 lombok
14. 接口设计时, uid, appId 等参数必须从上下文组件 MobileAPIContext 中获取，不能依赖传参，更安全
15. 打印日志不要用 `=`，用 `:`，如用 `uid: {}`，而不是 `uid={}`, `=` 在分词上有歧义，不利于日志查找
