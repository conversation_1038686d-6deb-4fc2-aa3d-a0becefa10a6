---
description: Controller 接口及Controller 实现类代码规范
globs: 
alwaysApply: false
---

注意 @CommonExecutor, @MobileAPI 都是放在接口上的，不要放在实现类上

#### 参数规范
1. dto 类和 request 类需要实现 Serializable 接口和使用 @Data 注解
2. 接口的参数必须包装成一个 request 类

#### Controller 接口代码规范
1. 普通 dubbo 接口，服务之间互相调用 RPC 用，只需要 @CommonExecutor 注解即可，示例如下
```java
import com.yupaopao.live.gateway.api.request.game.BarrageGameAppStartRequest;
import com.yupaopao.live.gateway.api.request.game.BarrageGameStartCloudDto;
import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;

/**
 * 弹幕游戏，pc端直接会在本地拉起游戏，所以后端记个状态就可以
 * app端因为手机配置不够，只能利用腾讯的云渲染能力，增加了排队机制，排队利用云主机
 */
public interface BarrageGameApi {
   /**
    * APP端开启弹幕游戏
    * app端因为手机配置不够，只能利用腾讯的云渲染能力，增加了排队机制，排队利用云主机
    *
    * @param request 参数
    * @return dto
    */
   @CommonExecutor(desc = "开始游戏", printParam = true, printResponse = true)
   Response<BarrageGameStartCloudDto> startCloud(BarrageGameAppStartRequest request);
}
```
2. 网关接口, mobile api 接口，给客户端调用用的，需要 @CommonExecutor 和 @MobileAPI 注解
```
import com.yupaopao.arthur.sdk.mobileapi.annotations.MobileAPI;
import com.yupaopao.live.gateway.api.request.game.BarrageGameAppStartRequest;
import com.yupaopao.live.gateway.api.request.game.BarrageGameStartCloudDto;
import com.yupaopao.platform.common.annotation.executor.CommonExecutor;
import com.yupaopao.platform.common.dto.Response;

/**
 * 弹幕游戏，pc端直接会在本地拉起游戏，所以后端记个状态就可以
 * app端因为手机配置不够，只能利用腾讯的云渲染能力，增加了排队机制，排队利用云主机
 */
@MobileAPI(path = "/game/barrage")
public interface BarrageGameApi {
    /**
     * APP端开启弹幕游戏
     * app端因为手机配置不够，只能利用腾讯的云渲染能力，增加了排队机制，排队利用云主机
     *
     * @param request 参数
     * @return bool
     */
    @MobileAPI(path = "/startCloud")
    @CommonExecutor(desc = "开始游戏", printParam = true, printResponse = true)
    Response<BarrageGameStartCloudDto> startCloud(BarrageGameAppStartRequest request);
}
```


##### Controller
非 SpringBoot 的 Controller， 而是 Dubbo 直接继承 api 模块接口的实现类，Controller 之间不互相调用，只调用 Manager。
规范：
1. 无需异常 catch，有默认的全局异常处理了
2. 如果需要上下文信息（MobileAPIContext 里的），会在这一层获取
3. 业务逻辑，包括参数校验不在这一层做，在 Manager 层做，Controller 层只是透传和简单调用
4. 不使用 Response<Void> 作为返回，至少使用 Response<Boolean>，哪怕最后直接返回 true
5. 使用 @DubboService 注解
   代码示例如下：
```java
package com.yupaopao.live.domain.core.service;

import com.yupaopao.live.domain.api.dto.LiveRoomInfoResponse;
import com.yupaopao.live.domain.api.req.LiveRoomInfoRequest;
import com.yupaopao.live.domain.api.service.LiveRoomService;
import com.yupaopao.live.domain.core.manager.LiveRoomManager;
import com.yupaopao.platform.common.dto.Response;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class LiveRoomServiceImpl implements LiveRoomService {
   @Resource
   private LiveRoomManager liveRoomManager;

   @Override
   public Response<LiveRoomInfoResponse> getLiveRoomInfo(LiveRoomInfoRequest request) {
      Long uid = MobileAPIContext.get(MobileAPIParamEnum.USER_UID.getValue(), Long.class);
      LiveRoomInfoResponse liveRoomInfo = liveRoomManager.getLiveRoomInfo(request.getLiveRoomId(), uid);
      return Response.success(liveRoomInfo);
   }
}
```
