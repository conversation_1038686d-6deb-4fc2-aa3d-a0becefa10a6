# AI Code Review Service

基于 Go 语言开发的智能代码审查微服务，支持多种审查模式、智能上下文分析和钉钉通知推送。

## ✨ 核心特性

- 🚀 **高性能架构**：Go 实现，启动快、资源占用低
- 🤖 **智能审查引擎**：集成 LLM（OpenAI/Claude）进行代码质量分析
- 🔄 **多种触发方式**：GitLab Webhook 自动触发 + 手动审查接口
- 📱 **钉钉集成**：实时推送审查结果和通知
- 🎯 **灵活配置**：支持分支过滤、审查风格自定义
- 🧠 **上下文感知**：结合代码依赖、函数调用等深度分析
- 📊 **多模式支持**：单个commit、commit范围、分支比较审查
- 🔧 **动态配置**：支持数据库级别的钉钉通知配置管理

---

## 📦 项目架构

```
ai-codereview-service/
├── cmd/server/                 # 服务入口
├── internal/
│   ├── config/                # 配置管理
│   ├── handler/               # HTTP处理器
│   │   ├── webhook_handler.go        # GitLab Webhook和手动审查
│   │   └── dingtalk_config_handler.go # 钉钉配置管理
│   ├── service/               # 核心业务逻辑
│   │   ├── review_service.go         # 主审查服务
│   │   ├── enhanced_reviewer.go      # 增强审查器
│   │   ├── unified_review_engine.go  # 统一审查引擎
│   │   ├── smart_truncator.go        # 智能截断器
│   │   └── context.go               # 上下文提取器
│   ├── repository/            # 数据访问层
│   ├── gitlab/                # GitLab API客户端
│   ├── llm/                   # LLM客户端
│   └── notification/          # 钉钉通知服务
├── pkg/logger/                # 日志工具
├── configs/                   # 配置文件
├── docs/                      # 文档目录
├── java_standard/             # Java代码规范
├── Dockerfile                 # Docker构建
└── README.md
```

---

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd ai-codereview-service

# 安装依赖
go mod tidy
```

### 2. 配置服务

创建配置文件 `configs/config.yaml`：

```yaml
server:
  port: 5003
  host: "0.0.0.0"

gitlab:
  url: "https://git.yupaopao.com"
  token: "your-gitlab-token"
  target_branches:
    - "test"
    - "master"
  default_branch: "test"
  comment_enabled: false

llm:
  provider: "openai"
  api_key: "your-api-key"
  base_url: "https://llm.yupaopao.com/v1"
  model: "anthropic/claude-sonnet-4"
  max_tokens: 4000
  timeout: 600

dingtalk:
  enabled: true
  webhook_url: "https://oapi.dingtalk.com/robot/send?access_token=your-token"

database:
  type: "mysql"
  dsn: "root:password@tcp(*************:3306)/ai_codereview?charset=utf8mb4&parseTime=True&loc=Local"

review:
  enabled: true
  auto_enabled: false  # 全局自动审查开关
  style: "professional"
  max_tokens: 80000
  enhanced_enabled: true

logging:
  level: "info"
  file: "./log/app.log"

async:
  enabled: true
```

### 3. 启动服务

```bash
# 方式一：直接运行
go run cmd/server/main.go

# 方式二：编译后运行
go build -o server cmd/server/main.go
./server

# 方式三：Docker运行
docker build -t ai-codereview .
docker run -p 5003:5003 ai-codereview
```

### 4. 配置 GitLab Webhook

在 GitLab 项目设置中添加 Webhook：
- **URL**: `http://your-server:5003/api/v1/webhook`
- **触发事件**: Push events, Merge request events
- **Secret Token**: (可选)

---

## 🔍 API 接口详解

### 统一API路径结构

所有API都使用 `/api/v1` 前缀，采用RESTful风格设计。

| 功能分类 | API路径 | 方法 | 描述 |
|---------|---------|------|------|
| **GitLab Webhook** | `/api/v1/webhook` | POST | GitLab webhook接收 |
| **代码审查** | `/api/v1/review/manual` | POST | 手动代码审查 |
| **系统状态** | `/api/v1/system/health` | GET | 健康检查 |
| **系统状态** | `/api/v1/system/stats` | GET | 系统统计 |
| **钉钉配置** | `/api/v1/dingtalk-configs` | POST | 创建钉钉配置 |
| **钉钉配置** | `/api/v1/dingtalk-configs` | GET | 获取钉钉配置列表 |
| **钉钉配置** | `/api/v1/dingtalk-configs/:id` | GET/PUT/DELETE | 单个钉钉配置操作 |

### 手动审查接口 - 四种模式

#### 🆕 模式1：单个Commit审查（最简单）

**特点**：只需提供项目名称和commit SHA，系统自动获取所有变更。

```bash
curl -X POST "http://localhost:5003/api/v1/review/manual" \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "ai-codereview-service",
    "commit": "9a226d57"
  }'
```

#### 模式2：Commit范围审查

**特点**：比较两个commit之间的累积差异，适合审查功能开发的多个commit。

```bash
curl -X POST "http://localhost:5003/api/v1/review/manual" \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "ai-codereview-service",
    "from_commit": "ab250b57",
    "to_commit": "9a226d57"
  }'
```

#### 🆕 模式3：分支比较审查（使用项目名称）

**特点**：支持使用项目名称进行分支比较，无需查找项目ID。

```bash
curl -X POST "http://localhost:5003/api/v1/review/manual" \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "ai-codereview-service",
    "source_branch": "feature-branch",
    "target_branch": "master"
  }'
```

#### 模式4：分支比较审查（使用项目ID，保持兼容）

```bash
curl -X POST "http://localhost:5003/api/v1/review/manual" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": 123,
    "source_branch": "feature-branch",
    "target_branch": "master"
  }'
```

### 请求参数说明

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `project_name` | string | 条件必填 | 项目名称（推荐使用） |
| `project_id` | int | 条件必填 | 项目ID（兼容模式） |
| `commit` | string | 条件必填 | 单个commit SHA |
| `from_commit` | string | 条件必填 | 起始commit SHA |
| `to_commit` | string | 条件必填 | 结束commit SHA |
| `source_branch` | string | 条件必填 | 源分支名 |
| `target_branch` | string | 条件必填 | 目标分支名 |
| `branch` | string | 可选 | 指定分支（用于文件获取） |
| `gitlab_url` | string | 可选 | GitLab URL（覆盖配置） |
| `gitlab_token` | string | 可选 | GitLab Token（覆盖配置） |

### 响应格式

```json
{
  "message": "Manual review request received, processing asynchronously",
  "project_name": "ai-codereview-service",
  "commit": "9a226d57",
  "mode": "single_commit"
}
```

**模式标识**：
- `single_commit`: 单个commit审查
- `commit_range`: commit范围审查
- `branch_compare_by_name`: 分支比较（项目名称）
- `branch_compare_by_id`: 分支比较（项目ID）

### 钉钉配置管理API

系统支持动态配置不同项目、分支的钉钉通知，优先级高于配置文件。

#### 创建钉钉配置

```bash
curl -X POST "http://localhost:5003/api/v1/dingtalk-configs" \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "ai-codereview-service",
    "branch": "master",
    "event_type": "push",
    "token": "5bca29d296c3cb44188723421bf2e3d5e8740b882fdc8f59222558a61f8fb90"
  }'
```

#### 获取钉钉配置

```bash
# 获取所有配置
curl "http://localhost:5003/api/v1/dingtalk-configs"

# 按项目名称过滤
curl "http://localhost:5003/api/v1/dingtalk-configs?project_name=ai-codereview-service"

# 按分支过滤
curl "http://localhost:5003/api/v1/dingtalk-configs?branch=master"
```

### 系统状态API

#### 健康检查

```bash
curl "http://localhost:5003/api/v1/system/health"
```

响应：
```json
{
  "status": "ok",
  "service": "ai-codereview-gitlab"
}
```

#### 系统统计

```bash
curl "http://localhost:5003/api/v1/system/stats"
```

---

## 🧠 智能审查特性

### 统一审查引擎

- **多层次分析**：语法、逻辑、性能、安全性
- **上下文感知**：函数调用关系、依赖分析
- **智能截断**：优化长内容处理，保持关键信息
- **语言特定**：针对Java、Go、Python等语言的专门优化

### 增强审查功能

- **递归依赖分析**：追踪代码间的复杂依赖关系
- **项目结构分析**：理解项目架构和模块关系
- **Tree-sitter解析**：精确的语法树分析
- **智能分层处理**：根据变更复杂度调整审查深度

### 配置优化

```yaml
review:
  enabled: true              # 总开关：控制所有审查功能
  auto_enabled: false        # 自动审查开关：关闭webhook自动审查
  style: "professional"      # 审查风格
  max_tokens: 80000         # 80k tokens上下文限制
  enhanced_enabled: true     # 启用增强审查
```

---

## 📊 监控和日志

### 日志配置

```yaml
logging:
  level: "info"              # debug, info, warn, error
  file: "./log/app.log"      # 日志文件路径
```

### 健康检查

```bash
# 检查服务状态
curl http://localhost:5003/api/v1/system/health

# 获取统计信息
curl http://localhost:5003/api/v1/system/stats
```

---

## 🔧 自动审查配置

### 配置优先级

1. **数据库配置优先**：如果项目在数据库中有钉钉配置，自动启用该项目的代码审查
2. **全局配置兜底**：如果没有数据库配置，使用 `config.yaml` 中的 `review.auto_enabled` 设置

### 启用项目自动审查

```bash
# 为项目配置钉钉通知即可自动启用代码审查
curl -X POST "http://localhost:5003/api/v1/dingtalk-configs" \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "your-project",
    "branch": "master", 
    "event_type": "push",
    "token": "your-dingtalk-token"
  }'
```

---

## 🔧 故障排查

### 常见问题

1. **项目ID找错问题**
   - 系统会自动验证项目ID与commit的匹配性
   - 支持智能项目ID修正和搜索

2. **webhook未触发**
   - 检查GitLab webhook配置URL是否正确：`http://your-server:5003/api/v1/webhook`
   - 检查服务器端口5003是否可访问
   - 查看日志确认是否收到webhook请求

3. **审查结果为空**
   - 检查LLM配置和API密钥
   - 验证网络连接
   - 查看token使用情况

### 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=debug
go run cmd/server/main.go
```

---

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t ai-codereview:latest .

# 运行容器
docker run -d \
  --name ai-codereview \
  -p 5003:5003 \
  -v $(pwd)/configs:/app/configs \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/log:/app/log \
  ai-codereview:latest
```

### 生产环境配置

```yaml
server:
  port: 5003
  host: "0.0.0.0"

database:
  type: "mysql"  # 生产环境推荐使用MySQL
  dsn: "user:password@tcp(localhost:3306)/ai_codereview?charset=utf8mb4&parseTime=True&loc=Local"

logging:
  level: "info"
  file: "/var/log/ai-codereview/app.log"
```

---

## 📈 性能优化

### 配置建议

- **Token限制**：根据实际需求调整max_tokens
- **并发控制**：合理设置异步处理队列大小
- **缓存策略**：启用文件内容缓存
- **数据库优化**：定期清理历史审查记录

### 监控指标

- 审查响应时间
- Token使用量
- 错误率统计
- 钉钉通知成功率

---

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

---

## 📄 许可证

本项目采用 MIT 许可证。详情请见 [LICENSE](LICENSE) 文件。

---