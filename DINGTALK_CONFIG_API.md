# 钉钉通知配置管理 API

本文档描述了如何使用钉钉通知配置管理API来动态配置不同项目、分支和事件类型的钉钉通知。

## 功能概述

钉钉通知配置管理功能允许您：

1. **动态配置钉钉通知**：为不同的项目、分支和事件类型配置不同的钉钉机器人token
2. **支持多种事件类型**：支持 `push` 和 `merge_request` 事件
3. **分支级别配置**：对于merge_request事件，使用target_branch进行匹配
4. **默认配置兜底**：如果数据库中没有匹配的配置，会使用config.yaml中的默认配置
5. **自动URL拼接**：系统会自动将token拼接为完整的钉钉webhook地址

## 数据模型

钉钉通知配置包含以下字段：

```json
{
  "id": 1,
  "project_name": "my-project",
  "branch": "master",
  "event_type": "push",
  "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

字段说明：
- `id`: 配置的唯一标识符
- `project_name`: 项目名称（必需）
- `branch`: 分支名称（必需）
- `event_type`: 事件类型，可选值：`push`, `merge_request`（必需）
- `token`: 钉钉机器人的access_token（必需，最少32个字符）

**注意**：系统会自动将token拼接为完整的webhook地址：
`https://oapi.dingtalk.com/robot/send?access_token={token}`

## API 接口

### 基础URL

```
http://localhost:5001/api/v1/dingtalk-configs
```

### 1. 创建钉钉通知配置

**请求**
```http
POST /api/v1/dingtalk-configs
Content-Type: application/json

{
  "project_name": "my-project",
  "branch": "master",
  "event_type": "push",
  "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
}
```

**字段说明**：
- `project_name`: 项目名称（必填）
- `branch`: 分支名称（必填）
- `event_type`: 事件类型，支持 `push` 和 `merge_request`（必填）
- `token`: 钉钉机器人的access_token（必填，最少32个字符）

**响应**
```json
{
  "message": "DingTalk notification configuration created successfully",
  "config": {
    "id": 1,
    "project_name": "my-project",
    "branch": "master",
    "event_type": "push",
    "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**
```json
{
  "error": "Configuration already exists for this project, branch and event type",
  "existing_config": { ... }
}
```

### 2. 获取钉钉通知配置列表

**请求**
```http
GET /api/v1/dingtalk-configs
```

**查询参数**（可选）：
- `project_name`: 按项目名称过滤
- `branch`: 按分支名称过滤
- `event_type`: 按事件类型过滤

**示例**
```http
GET /api/v1/dingtalk-configs?project_name=my-project&branch=master
```

**响应**
```json
{
  "configs": [
    {
      "id": 1,
      "project_name": "my-project",
      "branch": "master",
      "event_type": "push",
      "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1
}
```

### 3. 根据ID获取钉钉通知配置

**请求**
```http
GET /api/v1/dingtalk-configs/{id}
```

**响应**
```json
{
  "config": {
    "id": 1,
    "project_name": "my-project",
    "branch": "master",
    "event_type": "push",
    "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 4. 更新钉钉通知配置

**请求**
```http
PUT /api/v1/dingtalk-configs/{id}
Content-Type: application/json

{
  "token": "new_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
}
```

注意：所有字段都是可选的，只会更新提供的字段。

**响应**
```json
{
  "message": "DingTalk notification configuration updated successfully",
  "config": {
    "id": 1,
    "project_name": "my-project",
    "branch": "master",
    "event_type": "push",
    "token": "new_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:30:00Z"
  }
}
```

### 5. 删除钉钉通知配置

**请求**
```http
DELETE /api/v1/dingtalk-configs/{id}
```

**响应**
```json
{
  "message": "DingTalk notification configuration deleted successfully"
}
```

## 使用场景

### 场景1：不同分支配置不同的钉钉群

```bash
# 为master分支配置生产环境群
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "my-project",
    "branch": "master",
    "event_type": "push",
    "token": "prod_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
  }'

# 为dev分支配置开发环境群
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "my-project",
    "branch": "dev",
    "event_type": "push",
    "token": "dev_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
  }'
```

### 场景2：同一分支配置多种事件类型

```bash
# 为master分支的push事件配置通知
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "my-project",
    "branch": "master",
    "event_type": "push",
    "token": "push_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
  }'

# 为master分支的merge_request事件配置通知
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "my-project",
    "branch": "master",
    "event_type": "merge_request",
    "token": "mr_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
  }'
```

### 场景3：多个项目使用不同的钉钉群

```bash
# 项目A
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "project-a",
    "branch": "master",
    "event_type": "push",
    "token": "project_a_token"
  }'

# 项目B
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "project-b",
    "branch": "master",
    "event_type": "push",
    "token": "project_b_token"
  }'
```

## 匹配规则

### Push 事件匹配
- 使用推送的分支名称进行匹配
- 如果配置了 `project-name` + `dev` + `push`，则dev分支的push事件会触发该配置

### Merge Request 事件匹配
- 使用目标分支（target_branch）进行匹配
- 如果配置了 `project-name` + `master` + `merge_request`，则目标分支为master的MR会触发该配置

### 优先级
1. **数据库配置优先**：如果找到匹配的数据库配置，使用该配置发送通知
2. **默认配置兜底**：如果没有找到匹配的配置，使用 `config.yaml` 中的默认配置

## 钉钉Token获取

1. 在钉钉群中添加自定义机器人
2. 复制webhook地址：`https://oapi.dingtalk.com/robot/send?access_token=xxxxx`
3. 提取token部分：`xxxxx`
4. 在API中使用该token即可

**示例**：
- 完整webhook地址：`https://oapi.dingtalk.com/robot/send?access_token=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0`
- 只需要的token：`a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0`

## 错误处理

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 配置不存在
- `409 Conflict`: 配置已存在（相同项目、分支、事件类型的组合）
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式

```json
{
  "error": "错误描述",
  "details": "详细错误信息（可选）"
}
```

## 自动审核优先级

当项目在数据库中配置了钉钉通知后，会自动启用该项目的代码审核功能，优先级如下：

1. **MySQL项目配置**：如果项目在数据库中有钉钉配置，自动启用审核
2. **config.yaml全局配置**：如果项目没有数据库配置，使用全局配置的 `auto_enabled` 设置

这样确保了有钉钉通知需求的项目都会自动启用代码审核功能。 