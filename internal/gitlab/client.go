package gitlab

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

type Client struct {
	baseURL    string
	token      string
	projectID  string
	httpClient *http.Client
}

func NewClient(baseURL, token, projectID string) *Client {
	return &Client{
		baseURL:   strings.TrimSuffix(baseURL, "/"),
		token:     token,
		projectID: projectID,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *Client) makeRequest(method, endpoint string, body interface{}) ([]byte, error) {
	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	url := fmt.Sprintf("%s/api/v4%s", c.baseURL, endpoint)
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Private-Token", c.token)
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode >= 400 {
		logger.Warnf("GitLab API请求失败: %s %s, 状态码: %d", method, url, resp.StatusCode)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

// GetMergeRequestChanges 获取MR的变更
func (c *Client) GetMergeRequestChanges(mrIID int) ([]repository.GitLabChange, error) {
	endpoint := fmt.Sprintf("/projects/%s/merge_requests/%d/changes", c.projectID, mrIID)

	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var response struct {
		Changes []repository.GitLabChange `json:"changes"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return response.Changes, nil
}

// GetMergeRequestCommits 获取MR的提交
func (c *Client) GetMergeRequestCommits(mrIID int) ([]repository.GitLabCommitInfo, error) {
	endpoint := fmt.Sprintf("/projects/%s/merge_requests/%d/commits", c.projectID, mrIID)

	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var commits []repository.GitLabCommitInfo
	if err := json.Unmarshal(respBody, &commits); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return commits, nil
}

// AddMergeRequestNote 添加MR评论
func (c *Client) AddMergeRequestNote(mrIID int, note string) error {
	endpoint := fmt.Sprintf("/projects/%s/merge_requests/%d/notes", c.projectID, mrIID)

	body := map[string]string{
		"body": note,
	}

	_, err := c.makeRequest("POST", endpoint, body)
	return err
}

// CompareCommits 比较两个提交之间的差异
func (c *Client) CompareCommits(from, to string) ([]repository.GitLabChange, error) {
	endpoint := fmt.Sprintf("/projects/%s/repository/compare", c.projectID)

	// 构建查询参数
	params := url.Values{}
	params.Set("from", from)
	params.Set("to", to)

	fullURL := fmt.Sprintf("%s?%s", endpoint, params.Encode())

	respBody, err := c.makeRequest("GET", fullURL, nil)
	if err != nil {
		return nil, err
	}

	var response struct {
		Diffs []repository.GitLabChange `json:"diffs"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return response.Diffs, nil
}

// GetCommit 获取单个提交信息
func (c *Client) GetCommit(commitID string) (*repository.GitLabCommitInfo, error) {
	endpoint := fmt.Sprintf("/projects/%s/repository/commits/%s", c.projectID, commitID)

	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var commit repository.GitLabCommitInfo
	if err := json.Unmarshal(respBody, &commit); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &commit, nil
}

// AddCommitComment 添加提交评论
func (c *Client) AddCommitComment(commitID, note string) error {
	endpoint := fmt.Sprintf("/projects/%s/repository/commits/%s/comments", c.projectID, commitID)

	body := map[string]string{
		"note": note,
	}

	_, err := c.makeRequest("POST", endpoint, body)
	return err
}

// GetCommits 获取仓库提交列表
func (c *Client) GetCommits(refName string, since, until time.Time, page, perPage int) ([]repository.GitLabCommitInfo, error) {
	endpoint := fmt.Sprintf("/projects/%s/repository/commits", c.projectID)

	params := url.Values{}
	if refName != "" {
		params.Set("ref_name", refName)
	}
	if !since.IsZero() {
		params.Set("since", since.Format(time.RFC3339))
	}
	if !until.IsZero() {
		params.Set("until", until.Format(time.RFC3339))
	}
	params.Set("page", strconv.Itoa(page))
	params.Set("per_page", strconv.Itoa(perPage))

	fullURL := fmt.Sprintf("%s?%s", endpoint, params.Encode())

	respBody, err := c.makeRequest("GET", fullURL, nil)
	if err != nil {
		return nil, err
	}

	var commits []repository.GitLabCommitInfo
	if err := json.Unmarshal(respBody, &commits); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return commits, nil
}

// GetFileContent 获取文件内容 - 简化版，不再自动回退
func (c *Client) GetFileContent(filePath, ref string) (string, error) {
	// 对文件路径进行正确的URL编码
	encodedPath := url.PathEscape(filePath)
	endpoint := fmt.Sprintf("/projects/%s/repository/files/%s/raw", c.projectID, encodedPath)

	params := url.Values{}
	if ref != "" {
		params.Set("ref", ref)
	}

	fullURL := fmt.Sprintf("%s?%s", endpoint, params.Encode())
	logger.Infof("GitLab API请求: GET %s%s", c.baseURL, fullURL)

	respBody, err := c.makeRequest("GET", fullURL, nil)
	if err != nil {
		return "", fmt.Errorf("无法从分支 %s 获取文件 %s: %w", ref, filePath, err)
	}

	return string(respBody), nil
}

// GetFileContentFromBranch 从指定分支获取文件内容 - 简化版，不再回退
func (c *Client) GetFileContentFromBranch(filePath, branch string) (string, error) {
	if branch == "" {
		// 不再硬编码test分支，而是使用项目默认分支
		defaultBranch, err := c.GetDefaultBranch()
		if err != nil {
			logger.Warnf("无法获取项目默认分支，使用master: %v", err)
			branch = "master"
		} else {
			branch = defaultBranch
		}
	}

	logger.Infof("从分支获取文件: %s (分支: %s)", filePath, branch)

	// 直接调用GetFileContent，不再有任何回退逻辑
	return c.GetFileContent(filePath, branch)
}

// FilterChanges 过滤变更，排除不需要审查的文件
func FilterChanges(changes []repository.GitLabChange) []repository.GitLabChange {
	var filtered []repository.GitLabChange

	excludePatterns := []string{
		".md", ".txt", ".yml", ".yaml", ".json", ".xml",
		".gitignore", ".dockerignore", "Dockerfile",
		".png", ".jpg", ".jpeg", ".gif", ".svg",
		"package-lock.json", "yarn.lock", "go.sum",
	}

	for _, change := range changes {
		if change.DeletedFile {
			continue // 跳过删除的文件
		}

		shouldExclude := false
		for _, pattern := range excludePatterns {
			if strings.HasSuffix(strings.ToLower(change.NewPath), pattern) {
				shouldExclude = true
				break
			}
		}

		if !shouldExclude {
			filtered = append(filtered, change)
		}
	}

	return filtered
}

// GetProject 获取项目信息
func (c *Client) GetProject() (*repository.GitLabProject, error) {
	endpoint := fmt.Sprintf("/projects/%s", c.projectID)

	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var project repository.GitLabProject
	if err := json.Unmarshal(respBody, &project); err != nil {
		return nil, fmt.Errorf("failed to unmarshal project response: %w", err)
	}

	return &project, nil
}

// CompareBranches 比较两个分支之间的差异
func (c *Client) CompareBranches(from, to string) ([]repository.GitLabChange, error) {
	endpoint := fmt.Sprintf("/projects/%s/repository/compare", c.projectID)

	// 构建查询参数
	params := url.Values{}
	params.Set("from", from)
	params.Set("to", to)

	fullURL := fmt.Sprintf("%s?%s", endpoint, params.Encode())

	respBody, err := c.makeRequest("GET", fullURL, nil)
	if err != nil {
		return nil, err
	}

	var response struct {
		Diffs []repository.GitLabChange `json:"diffs"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return response.Diffs, nil
}

// FindProjectByName 通过项目名称查找项目ID
func (c *Client) FindProjectByName(projectName string) (int, error) {
	endpoint := "/projects"

	// 构建查询参数
	params := url.Values{}
	params.Set("search", projectName)
	params.Set("per_page", "100")

	fullURL := fmt.Sprintf("%s?%s", endpoint, params.Encode())

	respBody, err := c.makeRequest("GET", fullURL, nil)
	if err != nil {
		return 0, err
	}

	var projects []repository.GitLabProject
	if err := json.Unmarshal(respBody, &projects); err != nil {
		return 0, fmt.Errorf("failed to unmarshal projects response: %w", err)
	}

	// 查找完全匹配的项目名称
	for _, project := range projects {
		if project.Name == projectName || project.PathWithNamespace == projectName {
			return project.ID, nil
		}
	}

	// 如果没有完全匹配，返回第一个包含该名称的项目
	for _, project := range projects {
		if strings.Contains(project.Name, projectName) {
			logger.Warnf("Project exact match not found, using partial match: %s (ID: %d)", project.Name, project.ID)
			return project.ID, nil
		}
	}

	return 0, fmt.Errorf("project not found: %s", projectName)
}

// GetCommitDiff 获取单个提交的差异信息
func (c *Client) GetCommitDiff(commitID string) ([]repository.GitLabChange, error) {
	endpoint := fmt.Sprintf("/projects/%s/repository/commits/%s/diff", c.projectID, commitID)

	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var changes []repository.GitLabChange
	if err := json.Unmarshal(respBody, &changes); err != nil {
		return nil, fmt.Errorf("failed to unmarshal commit diff response: %w", err)
	}

	return changes, nil
}

// CheckFileExistsInCommit 检查文件在指定commit中是否存在
func (c *Client) CheckFileExistsInCommit(filePath, commitID string) (bool, error) {
	encodedPath := url.PathEscape(filePath)
	endpoint := fmt.Sprintf("/projects/%s/repository/files/%s", c.projectID, encodedPath)

	params := url.Values{}
	params.Set("ref", commitID)

	fullURL := fmt.Sprintf("%s?%s", endpoint, params.Encode())
	logger.Infof("检查文件是否存在: GET %s%s", c.baseURL, fullURL)

	_, err := c.makeRequest("GET", fullURL, nil)
	if err != nil {
		if strings.Contains(err.Error(), "404") {
			return false, nil // 文件不存在
		}
		return false, err // 其他错误
	}

	return true, nil // 文件存在
}

// ListRepositoryTree 列出仓库指定分支的文件树
func (c *Client) ListRepositoryTree(branch string, path string) ([]map[string]interface{}, error) {
	if branch == "" {
		branch = "test" // 改为test分支，适配您的项目环境
	}

	url := fmt.Sprintf("%s/api/v4/projects/%s/repository/tree", c.baseURL, c.projectID)

	// 构建请求参数
	params := map[string]string{
		"ref": branch,
	}

	if path != "" {
		params["path"] = path
	}

	// 添加参数到URL
	if len(params) > 0 {
		query := make([]string, 0, len(params))
		for k, v := range params {
			query = append(query, fmt.Sprintf("%s=%s", k, v))
		}
		url += "?" + strings.Join(query, "&")
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("PRIVATE-TOKEN", c.token)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求执行失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("获取文件树失败: status=%d, body=%s", resp.StatusCode, string(body))
	}

	var tree []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&tree); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return tree, nil
}

// GetCommitBranches 获取包含指定commit的分支列表
func (c *Client) GetCommitBranches(commitID string) ([]string, error) {
	endpoint := fmt.Sprintf("/projects/%s/repository/commits/%s/refs", c.projectID, commitID)

	respBody, err := c.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var refs []struct {
		Type string `json:"type"`
		Name string `json:"name"`
	}

	if err := json.Unmarshal(respBody, &refs); err != nil {
		return nil, fmt.Errorf("failed to unmarshal refs response: %w", err)
	}

	var branches []string
	for _, ref := range refs {
		if ref.Type == "branch" {
			branches = append(branches, ref.Name)
		}
	}

	return branches, nil
}

// GetDefaultBranch 获取项目的默认分支
func (c *Client) GetDefaultBranch() (string, error) {
	project, err := c.GetProject()
	if err != nil {
		return "", err
	}

	if project.DefaultBranch != "" {
		return project.DefaultBranch, nil
	}

	// 如果没有设置默认分支，尝试常见的默认分支名
	commonDefaults := []string{"main", "master", "develop"}
	for _, branch := range commonDefaults {
		// 尝试获取分支信息来验证是否存在
		endpoint := fmt.Sprintf("/projects/%s/repository/branches/%s", c.projectID, branch)
		_, err := c.makeRequest("GET", endpoint, nil)
		if err == nil {
			return branch, nil
		}
	}

	return "master", nil // 最后的默认值
}
