package notification

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

type DingTalkNotifier struct {
	webhookURL string
	enabled    bool
	httpClient *http.Client
}

type DingTalkMessage struct {
	MsgType  string            `json:"msgtype"`
	Text     *DingTalkText     `json:"text,omitempty"`
	Markdown *DingTalkMarkdown `json:"markdown,omitempty"`
	At       *DingTalkAt       `json:"at,omitempty"`
}

type DingTalkText struct {
	Content string `json:"content"`
}

type DingTalkMarkdown struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}

type DingTalkAt struct {
	AtMobiles []string `json:"atMobiles,omitempty"`
	IsAtAll   bool     `json:"isAtAll"`
}

func NewDingTalkNotifier(webhookURL string, enabled bool) *DingTalkNotifier {
	return &DingTalkNotifier{
		webhookURL: webhookURL,
		enabled:    enabled,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

func (d *DingTalkNotifier) SendText(content string, isAtAll bool) error {
	if !d.enabled {
		return nil
	}

	message := DingTalkMessage{
		MsgType: "text",
		Text: &DingTalkText{
			Content: content,
		},
		At: &DingTalkAt{
			IsAtAll: isAtAll,
		},
	}

	return d.sendMessage(message)
}

func (d *DingTalkNotifier) SendMarkdown(title, content string, isAtAll bool) error {
	if !d.enabled {
		return nil
	}

	message := DingTalkMessage{
		MsgType: "markdown",
		Markdown: &DingTalkMarkdown{
			Title: title,
			Text:  content,
		},
		At: &DingTalkAt{
			IsAtAll: isAtAll,
		},
	}

	return d.sendMessage(message)
}

func (d *DingTalkNotifier) sendMessage(message DingTalkMessage) error {
	jsonBody, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	req, err := http.NewRequest("POST", d.webhookURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("DingTalk API returned status %d", resp.StatusCode)
	}

	// 解析响应检查是否成功
	var response struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if response.ErrCode != 0 {
		return fmt.Errorf("DingTalk API error: %s (code: %d)", response.ErrMsg, response.ErrCode)
	}

	return nil
}

// FormatChinaTime 格式化为中国时区时间
func FormatChinaTime(timestamp string) string {
	// 解析时间戳
	layouts := []string{
		time.RFC3339,
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02T15:04:05+00:00",
	}

	var t time.Time
	var err error

	for _, layout := range layouts {
		t, err = time.Parse(layout, timestamp)
		if err == nil {
			break
		}
	}

	if err != nil {
		return timestamp // 如果解析失败，返回原始时间戳
	}

	// 转换为中国时区 (UTC+8)
	chinaLocation := time.FixedZone("CST", 8*3600)
	chinaTime := t.In(chinaLocation)

	return chinaTime.Format("2006-01-02 15:04:05 (北京时间)")
}
