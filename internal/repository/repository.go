package repository

import (
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type DBRepository struct {
	db *gorm.DB
}

func New(dbType, dsn string) (*DBRepository, error) {
	var db *gorm.DB
	var err error

	switch dbType {
	case "mysql":
		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Silent),
		})
	case "sqlite":
		fallthrough
	default:
		db, err = gorm.Open(sqlite.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Silent),
		})
	}
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %w", err)
	}

	// 自动迁移数据库表
	if err := db.AutoMigrate(&PushReviewLog{}, &MergeRequestReviewLog{}, &DingTalkNotificationConfig{}); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	// 处理钉钉配置表的字段迁移：webhook_url -> token
	if err := migrateDingTalkConfigFields(db); err != nil {
		return nil, fmt.Errorf("failed to migrate dingtalk config fields: %w", err)
	}

	return &DBRepository{db: db}, nil
}

// InsertPushReviewLog 插入Push审查记录
func (r *DBRepository) InsertPushReviewLog(log *PushReviewLog) error {
	return r.db.Create(log).Error
}

// InsertMRReviewLog 插入MR审查记录
func (r *DBRepository) InsertMRReviewLog(log *MergeRequestReviewLog) error {
	return r.db.Create(log).Error
}

// IsPushAlreadyReviewed 检查Push是否已经审查过
func (r *DBRepository) IsPushAlreadyReviewed(projectName, branch, beforeCommit, afterCommit string) (bool, error) {
	var count int64
	err := r.db.Model(&PushReviewLog{}).
		Where("project_name = ? AND branch = ? AND before_commit = ? AND after_commit = ?",
			projectName, branch, beforeCommit, afterCommit).
		Count(&count).Error

	return count > 0, err
}

// GetPushReviewLogs 获取Push审查记录
func (r *DBRepository) GetPushReviewLogs(projectName string, limit int) ([]PushReviewLog, error) {
	var logs []PushReviewLog
	err := r.db.Where("project_name = ?", projectName).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error

	return logs, err
}

// GetMRReviewLogs 获取MR审查记录
func (r *DBRepository) GetMRReviewLogs(projectName string, limit int) ([]MergeRequestReviewLog, error) {
	var logs []MergeRequestReviewLog
	err := r.db.Where("project_name = ?", projectName).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error

	return logs, err
}

// CleanupOldLogs 清理旧的日志记录
func (r *DBRepository) CleanupOldLogs(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)

	// 清理Push审查记录
	if err := r.db.Where("created_at < ?", cutoff).Delete(&PushReviewLog{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup push review logs: %w", err)
	}

	// 清理MR审查记录
	if err := r.db.Where("created_at < ?", cutoff).Delete(&MergeRequestReviewLog{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup mr review logs: %w", err)
	}

	return nil
}

// GetTodayStats 获取今日统计
func (r *DBRepository) GetTodayStats() (map[string]int64, error) {
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)

	stats := make(map[string]int64)

	// 统计今日Push审查数量
	var pushCount int64
	if err := r.db.Model(&PushReviewLog{}).
		Where("created_at >= ? AND created_at < ?", today, tomorrow).
		Count(&pushCount).Error; err != nil {
		return nil, err
	}
	stats["push_reviews"] = pushCount

	// 统计今日MR审查数量
	var mrCount int64
	if err := r.db.Model(&MergeRequestReviewLog{}).
		Where("created_at >= ? AND created_at < ?", today, tomorrow).
		Count(&mrCount).Error; err != nil {
		return nil, err
	}
	stats["mr_reviews"] = mrCount

	return stats, nil
}

// CreateDingTalkNotificationConfig 创建钉钉通知配置
func (r *DBRepository) CreateDingTalkNotificationConfig(config *DingTalkNotificationConfig) error {
	return r.db.Create(config).Error
}

// GetDingTalkNotificationConfigs 获取钉钉通知配置列表
func (r *DBRepository) GetDingTalkNotificationConfigs(projectName, branch, eventType string) ([]DingTalkNotificationConfig, error) {
	var configs []DingTalkNotificationConfig
	query := r.db.Model(&DingTalkNotificationConfig{})

	if projectName != "" {
		query = query.Where("project_name = ?", projectName)
	}
	if branch != "" {
		query = query.Where("branch = ?", branch)
	}
	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	err := query.Order("created_at DESC").Find(&configs).Error
	return configs, err
}

// GetDingTalkNotificationConfigByID 根据ID获取钉钉通知配置
func (r *DBRepository) GetDingTalkNotificationConfigByID(id uint) (*DingTalkNotificationConfig, error) {
	var config DingTalkNotificationConfig
	err := r.db.First(&config, id).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// UpdateDingTalkNotificationConfig 更新钉钉通知配置
func (r *DBRepository) UpdateDingTalkNotificationConfig(config *DingTalkNotificationConfig) error {
	return r.db.Save(config).Error
}

// DeleteDingTalkNotificationConfig 删除钉钉通知配置
func (r *DBRepository) DeleteDingTalkNotificationConfig(id uint) error {
	return r.db.Delete(&DingTalkNotificationConfig{}, id).Error
}

// GetMatchingDingTalkConfigs 根据项目名称、分支和事件类型获取匹配的钉钉配置
func (r *DBRepository) GetMatchingDingTalkConfigs(projectName, branch, eventType string) ([]DingTalkNotificationConfig, error) {
	var configs []DingTalkNotificationConfig
	err := r.db.Where("project_name = ? AND branch = ? AND event_type = ?",
		projectName, branch, eventType).Find(&configs).Error
	return configs, err
}

// HasDingTalkConfigForProject 检查某个项目是否有钉钉配置（用于判断是否启用自动审核）
func (r *DBRepository) HasDingTalkConfigForProject(projectName string) (bool, error) {
	var count int64
	err := r.db.Model(&DingTalkNotificationConfig{}).
		Where("project_name = ?", projectName).
		Count(&count).Error
	return count > 0, err
}

// migrateDingTalkConfigFields 处理钉钉配置表的字段迁移：webhook_url -> token
func migrateDingTalkConfigFields(db *gorm.DB) error {
	// 检查是否需要迁移（从 webhook_url 字段迁移到 token 字段）
	// 这里可以根据需要添加具体的迁移逻辑
	// 目前先简单返回 nil，因为新的表结构已经使用了 token 字段
	return nil
}
