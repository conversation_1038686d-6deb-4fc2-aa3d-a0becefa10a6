package repository

import (
	"time"
)

// PushReviewLog Push审查记录
type PushReviewLog struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	ProjectName  string    `gorm:"size:255;not null" json:"project_name"`
	Author       string    `gorm:"size:100;not null" json:"author"`
	Branch       string    `gorm:"size:100;not null" json:"branch"`
	BeforeCommit string    `gorm:"size:40" json:"before_commit"`
	AfterCommit  string    `gorm:"size:40" json:"after_commit"`
	Score        int       `gorm:"default:0" json:"score"`
	ReviewResult string    `gorm:"type:text" json:"review_result"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// MergeRequestReviewLog MR审查记录
type MergeRequestReviewLog struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	ProjectName     string    `gorm:"size:255;not null" json:"project_name"`
	Author          string    `gorm:"size:100;not null" json:"author"`
	SourceBranch    string    `gorm:"size:100;not null" json:"source_branch"`
	TargetBranch    string    `gorm:"size:100;not null" json:"target_branch"`
	MergeRequestIID int       `gorm:"not null" json:"merge_request_iid"`
	Score           int       `gorm:"default:0" json:"score"`
	ReviewResult    string    `gorm:"type:text" json:"review_result"`
	URL             string    `gorm:"size:500" json:"url"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// DingTalkNotificationConfig 钉钉通知配置
type DingTalkNotificationConfig struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	ProjectName string    `gorm:"size:255;not null;index" json:"project_name"` // 项目名称
	Branch      string    `gorm:"size:100;not null;index" json:"branch"`       // 分支名称
	EventType   string    `gorm:"size:50;not null;index" json:"event_type"`    // 事件类型：push 或 merge_request
	Token       string    `gorm:"size:200;not null" json:"token"`              // 钉钉机器人token
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// GitLabWebhook GitLab Webhook数据结构
type GitLabWebhook struct {
	ObjectKind        string           `json:"object_kind"`
	EventName         string           `json:"event_name"`
	Before            string           `json:"before"`
	After             string           `json:"after"`
	Ref               string           `json:"ref"`
	CheckoutSHA       string           `json:"checkout_sha"`
	UserID            int              `json:"user_id"`
	UserName          string           `json:"user_name"`
	UserUsername      string           `json:"user_username"`
	UserEmail         string           `json:"user_email"`
	UserAvatar        string           `json:"user_avatar"`
	ProjectID         int              `json:"project_id"`
	Project           GitLabProject    `json:"project"`
	Repository        GitLabRepository `json:"repository"`
	Commits           []Commit         `json:"commits"`
	TotalCommitsCount int              `json:"total_commits_count"`
	ObjectAttributes  ObjectAttributes `json:"object_attributes"`
	User              GitLabUser       `json:"user"`
}

// GitLabNamespace GitLab命名空间信息
type GitLabNamespace struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Path     string `json:"path"`
	Kind     string `json:"kind"`
	FullPath string `json:"full_path"`
	ParentID *int   `json:"parent_id"`
	WebURL   string `json:"web_url"`
}

// GitLabProject GitLab项目信息
type GitLabProject struct {
	ID                int             `json:"id"`
	Name              string          `json:"name"`
	Description       string          `json:"description"`
	WebURL            string          `json:"web_url"`
	AvatarURL         string          `json:"avatar_url"`
	GitSSHURL         string          `json:"git_ssh_url"`
	GitHTTPURL        string          `json:"git_http_url"`
	Namespace         GitLabNamespace `json:"namespace"`
	VisibilityLevel   int             `json:"visibility_level"`
	PathWithNamespace string          `json:"path_with_namespace"`
	DefaultBranch     string          `json:"default_branch"`
	Homepage          string          `json:"homepage"`
	URL               string          `json:"url"`
	SSHURL            string          `json:"ssh_url"`
	HTTPURL           string          `json:"http_url"`
}

type Project struct {
	ID                int    `json:"id"`
	Name              string `json:"name"`
	Description       string `json:"description"`
	WebURL            string `json:"web_url"`
	GitSSHURL         string `json:"git_ssh_url"`
	GitHTTPURL        string `json:"git_http_url"`
	Namespace         string `json:"namespace"`
	VisibilityLevel   int    `json:"visibility_level"`
	PathWithNamespace string `json:"path_with_namespace"`
	DefaultBranch     string `json:"default_branch"`
	Homepage          string `json:"homepage"`
	URL               string `json:"url"`
	SSHURL            string `json:"ssh_url"`
	HTTPURL           string `json:"http_url"`
}

type Commit struct {
	ID        string   `json:"id"`
	Message   string   `json:"message"`
	Timestamp string   `json:"timestamp"`
	URL       string   `json:"url"`
	Author    Author   `json:"author"`
	Added     []string `json:"added"`
	Modified  []string `json:"modified"`
	Removed   []string `json:"removed"`
}

type Author struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type Repository struct {
	Name        string `json:"name"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Homepage    string `json:"homepage"`
	GitHTTPURL  string `json:"git_http_url"`
	GitSSHURL   string `json:"git_ssh_url"`
}

// GitLabRepository GitLab仓库信息
type GitLabRepository struct {
	Name        string `json:"name"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Homepage    string `json:"homepage"`
	GitHTTPURL  string `json:"git_http_url"`
	GitSSHURL   string `json:"git_ssh_url"`
}

type User struct {
	Name      string `json:"name"`
	Username  string `json:"username"`
	AvatarURL string `json:"avatar_url"`
}

// GitLabUser GitLab用户信息
type GitLabUser struct {
	Name      string `json:"name"`
	Username  string `json:"username"`
	AvatarURL string `json:"avatar_url"`
	Email     string `json:"email"`
}

type ObjectAttributes struct {
	ID              int    `json:"id"`
	IID             int    `json:"iid"`
	Title           string `json:"title"`
	Description     string `json:"description"`
	State           string `json:"state"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
	TargetBranch    string `json:"target_branch"`
	SourceBranch    string `json:"source_branch"`
	SourceProjectID int    `json:"source_project_id"`
	TargetProjectID int    `json:"target_project_id"`
	AuthorID        int    `json:"author_id"`
	AssigneeID      int    `json:"assignee_id"`
	URL             string `json:"url"`
	Action          string `json:"action"`
	MergeStatus     string `json:"merge_status"`
}

// GitLabChange GitLab变更信息
type GitLabChange struct {
	NewPath     string `json:"new_path"`
	OldPath     string `json:"old_path"`
	AMode       string `json:"a_mode"`
	BMode       string `json:"b_mode"`
	NewFile     bool   `json:"new_file"`
	RenamedFile bool   `json:"renamed_file"`
	DeletedFile bool   `json:"deleted_file"`
	Diff        string `json:"diff"`
}

// GitLabCommitInfo GitLab提交信息
type GitLabCommitInfo struct {
	ID             string `json:"id"`
	ShortID        string `json:"short_id"`
	Title          string `json:"title"`
	Message        string `json:"message"`
	AuthorName     string `json:"author_name"`
	AuthorEmail    string `json:"author_email"`
	AuthoredDate   string `json:"authored_date"`
	CommitterName  string `json:"committer_name"`
	CommitterEmail string `json:"committer_email"`
	CommittedDate  string `json:"committed_date"`
	CreatedAt      string `json:"created_at"`
	WebURL         string `json:"web_url"`
}
