package handler

import (
	"net/http"
	"strconv"

	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"

	"github.com/gin-gonic/gin"
)

type DingTalkConfigHandler struct {
	repo *repository.DBRepository
}

// CreateDingTalkConfigRequest 创建钉钉通知配置请求
type CreateDingTalkConfigRequest struct {
	ProjectName string `json:"project_name" binding:"required"`
	Branch      string `json:"branch" binding:"required"`
	EventType   string `json:"event_type" binding:"required,oneof=push merge_request"`
	Token       string `json:"token" binding:"required,min=32"`
}

// UpdateDingTalkConfigRequest 更新钉钉通知配置请求
type UpdateDingTalkConfigRequest struct {
	ProjectName string `json:"project_name,omitempty"`
	Branch      string `json:"branch,omitempty"`
	EventType   string `json:"event_type,omitempty,oneof=push merge_request"`
	Token       string `json:"token,omitempty,min=32"`
}

func NewDingTalkConfigHandler(repo *repository.DBRepository) *DingTalkConfigHandler {
	return &DingTalkConfigHandler{
		repo: repo,
	}
}

// CreateDingTalkConfig 创建钉钉通知配置
func (h *DingTalkConfigHandler) CreateDingTalkConfig(c *gin.Context) {
	var req CreateDingTalkConfigRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse create dingtalk config request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	// 检查是否已存在相同配置
	existingConfigs, err := h.repo.GetMatchingDingTalkConfigs(req.ProjectName, req.Branch, req.EventType)
	if err != nil {
		logger.Errorf("Failed to check existing dingtalk configs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	if len(existingConfigs) > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error":           "Configuration already exists for this project, branch and event type",
			"existing_config": existingConfigs[0],
		})
		return
	}

	config := &repository.DingTalkNotificationConfig{
		ProjectName: req.ProjectName,
		Branch:      req.Branch,
		EventType:   req.EventType,
		Token:       req.Token,
	}

	if err := h.repo.CreateDingTalkNotificationConfig(config); err != nil {
		logger.Errorf("Failed to create dingtalk config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create configuration"})
		return
	}

	logger.Infof("Created dingtalk config: project=%s, branch=%s, event=%s",
		req.ProjectName, req.Branch, req.EventType)

	c.JSON(http.StatusCreated, gin.H{
		"message": "DingTalk notification configuration created successfully",
		"config":  config,
	})
}

// GetDingTalkConfigs 获取钉钉通知配置列表
func (h *DingTalkConfigHandler) GetDingTalkConfigs(c *gin.Context) {
	projectName := c.Query("project_name")
	branch := c.Query("branch")
	eventType := c.Query("event_type")

	configs, err := h.repo.GetDingTalkNotificationConfigs(projectName, branch, eventType)
	if err != nil {
		logger.Errorf("Failed to get dingtalk configs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"configs": configs,
		"total":   len(configs),
	})
}

// GetDingTalkConfig 根据ID获取钉钉通知配置
func (h *DingTalkConfigHandler) GetDingTalkConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	config, err := h.repo.GetDingTalkNotificationConfigByID(uint(id))
	if err != nil {
		logger.Errorf("Failed to get dingtalk config by ID %d: %v", id, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Configuration not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"config": config})
}

// UpdateDingTalkConfig 更新钉钉通知配置
func (h *DingTalkConfigHandler) UpdateDingTalkConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	var req UpdateDingTalkConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse update dingtalk config request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	// 获取现有配置
	config, err := h.repo.GetDingTalkNotificationConfigByID(uint(id))
	if err != nil {
		logger.Errorf("Failed to get dingtalk config by ID %d: %v", id, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Configuration not found"})
		return
	}

	// 更新字段
	if req.ProjectName != "" {
		config.ProjectName = req.ProjectName
	}
	if req.Branch != "" {
		config.Branch = req.Branch
	}
	if req.EventType != "" {
		config.EventType = req.EventType
	}
	if req.Token != "" {
		config.Token = req.Token
	}

	if err := h.repo.UpdateDingTalkNotificationConfig(config); err != nil {
		logger.Errorf("Failed to update dingtalk config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update configuration"})
		return
	}

	logger.Infof("Updated dingtalk config ID %d: project=%s, branch=%s, event=%s",
		id, config.ProjectName, config.Branch, config.EventType)

	c.JSON(http.StatusOK, gin.H{
		"message": "DingTalk notification configuration updated successfully",
		"config":  config,
	})
}

// DeleteDingTalkConfig 删除钉钉通知配置
func (h *DingTalkConfigHandler) DeleteDingTalkConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	// 检查配置是否存在
	_, err = h.repo.GetDingTalkNotificationConfigByID(uint(id))
	if err != nil {
		logger.Errorf("Failed to get dingtalk config by ID %d: %v", id, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Configuration not found"})
		return
	}

	if err := h.repo.DeleteDingTalkNotificationConfig(uint(id)); err != nil {
		logger.Errorf("Failed to delete dingtalk config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete configuration"})
		return
	}

	logger.Infof("Deleted dingtalk config ID %d", id)

	c.JSON(http.StatusOK, gin.H{
		"message": "DingTalk notification configuration deleted successfully",
	})
}
