package handler

import (
	"fmt"
	"net/http"

	"ai-codereview-service/internal/repository"
	"ai-codereview-service/internal/service"
	"ai-codereview-service/pkg/logger"

	"github.com/gin-gonic/gin"
)

// webhookPayload is a temporary struct to handle the "namespace" field in webhooks,
// which can be a string. The main GitLabProject struct expects it to be an object.
type webhookPayload struct {
	repository.GitLabWebhook
	Project struct {
		repository.GitLabProject
		Namespace string `json:"namespace"`
	} `json:"project"`
}

type WebhookHandler struct {
	reviewService *service.ReviewService
}

// ManualReviewRequest 手动审查请求结构
type ManualReviewRequest struct {
	// 支持两种格式：基于分支和基于commit
	ProjectID    int    `json:"project_id,omitempty"`
	ProjectName  string `json:"project_name,omitempty"` // 🆕 分支比较也支持项目名称
	SourceBranch string `json:"source_branch,omitempty"`
	TargetBranch string `json:"target_branch,omitempty"`

	// 新增：基于项目名称和commit的格式
	FromCommit string `json:"from_commit,omitempty"`
	ToCommit   string `json:"to_commit,omitempty"`

	// 🆕 新增：单个commit审查模式
	Commit string `json:"commit,omitempty"`

	// 新增：可选的分支参数，用于指定获取文件时使用的分支
	// 如果不指定，系统会尝试根据commit自动获取，或使用项目默认分支
	Branch string `json:"branch,omitempty"`

	GitLabURL   string `json:"gitlab_url,omitempty"`
	GitLabToken string `json:"gitlab_token,omitempty"`
}

// safeSubstring 安全的字符串截取函数
func safeSubstring(s string, length int) string {
	if len(s) <= length {
		return s
	}
	return s[:length]
}

func NewWebhookHandler(reviewService *service.ReviewService) *WebhookHandler {
	return &WebhookHandler{
		reviewService: reviewService,
	}
}

// HandleGitLabWebhook 处理GitLab webhook请求
func (h *WebhookHandler) HandleGitLabWebhook(c *gin.Context) {
	var payload webhookPayload

	if err := c.ShouldBindJSON(&payload); err != nil {
		logger.Errorf("Failed to parse webhook payload: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON payload"})
		return
	}

	// Transfer data from the temporary payload to the standard webhook struct.
	webhook := payload.GitLabWebhook
	webhook.Project = payload.Project.GitLabProject
	webhook.Project.Namespace = repository.GitLabNamespace{
		Name:     payload.Project.Namespace,
		FullPath: payload.Project.Namespace,
	}

	// 记录接收到的事件
	logger.Infof("Received GitLab webhook: %s", webhook.ObjectKind)

	// 异步处理webhook事件
	go h.processWebhookAsync(&webhook)

	// 立即返回响应
	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Request received (object_kind=%s), will process asynchronously", webhook.ObjectKind),
	})
}

func (h *WebhookHandler) processWebhookAsync(webhook *repository.GitLabWebhook) {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("Panic in webhook processing: %v", r)
		}
	}()

	switch webhook.ObjectKind {
	case "push":
		if err := h.reviewService.HandlePushEvent(webhook); err != nil {
			logger.Errorf("Failed to handle push event: %v", err)
		}
	case "merge_request":
		if err := h.reviewService.HandleMergeRequestEvent(webhook); err != nil {
			logger.Errorf("Failed to handle merge request event: %v", err)
		}
	default:
		logger.Warnf("Unsupported webhook event type: %s", webhook.ObjectKind)
	}
}

// HandleManualReview 处理手动审查请求
func (h *WebhookHandler) HandleManualReview(c *gin.Context) {
	var req ManualReviewRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse manual review request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	// 验证请求格式
	if !h.validateManualReviewRequest(&req) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": "Please provide one of: (project_name, commit) or (project_name, from_commit, to_commit) or (project_name, source_branch, target_branch) or (project_id, source_branch, target_branch)",
		})
		return
	}

	// 根据请求类型记录日志
	if req.ProjectName != "" {
		if req.Commit != "" {
			// 单个commit模式
			logger.Infof("Received single commit review request: project=%s, commit=%s",
				req.ProjectName, safeSubstring(req.Commit, 8))
		} else if req.FromCommit != "" && req.ToCommit != "" {
			// commit范围模式
			logger.Infof("Received commit-based manual review request: project=%s, from=%s, to=%s",
				req.ProjectName, safeSubstring(req.FromCommit, 8), safeSubstring(req.ToCommit, 8))
		} else {
			// 分支比较模式（使用项目名称）
			logger.Infof("Received branch-based manual review request: project=%s, source=%s, target=%s",
				req.ProjectName, req.SourceBranch, req.TargetBranch)
		}
	} else {
		// 分支比较模式（使用项目ID）
		logger.Infof("Received branch-based manual review request: project_id=%d, source=%s, target=%s",
			req.ProjectID, req.SourceBranch, req.TargetBranch)
	}

	// 异步处理手动审查
	go h.processManualReviewAsync(&req)

	// 构建响应
	response := gin.H{"message": "Manual review request received, processing asynchronously"}
	if req.ProjectName != "" {
		response["project_name"] = req.ProjectName
		if req.Commit != "" {
			response["commit"] = req.Commit
			response["mode"] = "single_commit"
		} else if req.FromCommit != "" && req.ToCommit != "" {
			response["from_commit"] = req.FromCommit
			response["to_commit"] = req.ToCommit
			response["mode"] = "commit_range"
		} else {
			response["source_branch"] = req.SourceBranch
			response["target_branch"] = req.TargetBranch
			response["mode"] = "branch_compare_by_name"
		}
	} else {
		response["project_id"] = req.ProjectID
		response["source_branch"] = req.SourceBranch
		response["target_branch"] = req.TargetBranch
		response["mode"] = "branch_compare_by_id"
	}

	c.JSON(http.StatusOK, response)
}

// validateManualReviewRequest 验证手动审查请求格式
func (h *WebhookHandler) validateManualReviewRequest(req *ManualReviewRequest) bool {
	// 🆕 格式1: 单个commit审查（最简单）
	if req.ProjectName != "" && req.Commit != "" {
		return true
	}

	// 格式2: 基于项目名称和commit范围
	if req.ProjectName != "" && req.FromCommit != "" && req.ToCommit != "" {
		return true
	}

	// 🆕 格式3: 基于项目名称和分支比较
	if req.ProjectName != "" && req.SourceBranch != "" && req.TargetBranch != "" {
		return true
	}

	// 格式4: 基于项目ID和分支比较（保持兼容）
	if req.ProjectID > 0 && req.SourceBranch != "" && req.TargetBranch != "" {
		return true
	}

	return false
}

// processManualReviewAsync 异步处理手动审查
func (h *WebhookHandler) processManualReviewAsync(req *ManualReviewRequest) {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("Panic in manual review processing: %v", r)
		}
	}()

	var err error

	// 根据请求格式调用不同的处理方法
	if req.ProjectName != "" {
		if req.Commit != "" {
			// 🆕 单个commit审查模式
			err = h.reviewService.HandleSingleCommitReview(
				req.ProjectName, req.Commit, req.Branch, req.GitLabURL, req.GitLabToken)
		} else if req.FromCommit != "" && req.ToCommit != "" {
			// 基于项目名称和commit范围的处理
			err = h.reviewService.HandleManualReviewByCommit(
				req.ProjectName, req.FromCommit, req.ToCommit, req.Branch, req.GitLabURL, req.GitLabToken)
		} else {
			// 🆕 基于项目名称和分支比较的处理
			err = h.reviewService.HandleManualReviewByProjectName(
				req.ProjectName, req.SourceBranch, req.TargetBranch, req.GitLabURL, req.GitLabToken)
		}
	} else {
		// 基于项目ID和分支的处理（保持兼容）
		err = h.reviewService.HandleManualReview(
			req.ProjectID, req.SourceBranch, req.TargetBranch, req.GitLabURL, req.GitLabToken)
	}

	if err != nil {
		logger.Errorf("Failed to handle manual review: %v", err)
	}
}

// HealthCheck 健康检查接口
func (h *WebhookHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "ai-codereview-gitlab",
	})
}

// GetStats 获取统计信息
func (h *WebhookHandler) GetStats(c *gin.Context) {
	// 这里可以添加统计信息的获取逻辑
	c.JSON(http.StatusOK, gin.H{
		"message": "Stats endpoint - to be implemented",
	})
}
