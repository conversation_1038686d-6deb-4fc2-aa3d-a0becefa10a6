package config

import (
	"fmt"

	"github.com/spf13/viper"
)

type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	GitLab   GitLabConfig   `mapstructure:"gitlab"`
	LLM      LLMConfig      `mapstructure:"llm"`
	DingTalk DingTalkConfig `mapstructure:"dingtalk"`
	Database DatabaseConfig `mapstructure:"database"`
	Review   ReviewConfig   `mapstructure:"review"`
	Logging  LoggingConfig  `mapstructure:"logging"`
	Async    AsyncConfig    `mapstructure:"async"`
}

type ServerConfig struct {
	Port int    `mapstructure:"port"`
	Host string `mapstructure:"host"`
}

type GitLabConfig struct {
	URL   string `mapstructure:"url"`
	Token string `mapstructure:"token"`
	// ProjectID 现在从webhook数据中动态获取，不再需要配置
	TargetBranches []string `mapstructure:"target_branches"`
	DefaultBranch  string   `mapstructure:"default_branch"` // 新增：默认分支配置
	CommentEnabled bool     `mapstructure:"comment_enabled"`
}

type LLMConfig struct {
	Provider  string `mapstructure:"provider"`
	APIKey    string `mapstructure:"api_key"`
	BaseURL   string `mapstructure:"base_url"`
	Model     string `mapstructure:"model"`
	MaxTokens int    `mapstructure:"max_tokens"`
}

type DingTalkConfig struct {
	Enabled    bool   `mapstructure:"enabled"`
	WebhookURL string `mapstructure:"webhook_url"`
}

type DatabaseConfig struct {
	Type string `mapstructure:"type"`
	DSN  string `mapstructure:"dsn"`
}

type ReviewConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	AutoEnabled     bool   `mapstructure:"auto_enabled"`
	Style           string `mapstructure:"style"`
	MaxTokens       int    `mapstructure:"max_tokens"`
	EnhancedEnabled bool   `mapstructure:"enhanced_enabled"`
}

type LoggingConfig struct {
	Level string `mapstructure:"level"`
	File  string `mapstructure:"file"`
}

type AsyncConfig struct {
	Enabled bool `mapstructure:"enabled"`
}

// ManualReviewConfig 定义了手动触发审查的配置
type ManualReviewConfig struct {
	ProjectID int    `mapstructure:"project_id" json:"project_id"`
	CommitSHA string `mapstructure:"commit_sha" json:"commit_sha"`
	Branch    string `mapstructure:"branch" json:"branch"` // 新增：手动审查的分支
}

var globalConfig *Config

func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	globalConfig = &config
	return &config, nil
}

func Get() *Config {
	return globalConfig
}

func setDefaults() {
	viper.SetDefault("server.port", 5001)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("gitlab.comment_enabled", false)
	viper.SetDefault("gitlab.default_branch", "test") // 默认为test分支，适配您的项目
	viper.SetDefault("llm.provider", "openai")
	viper.SetDefault("llm.model", "qwen-max")
	viper.SetDefault("llm.max_tokens", 4000)
	viper.SetDefault("llm.timeout", 300) // 默认5分钟超时
	viper.SetDefault("dingtalk.enabled", true)
	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.dsn", "./data/app.db")
	viper.SetDefault("review.enabled", true)
	viper.SetDefault("review.auto_enabled", true)
	viper.SetDefault("review.style", "professional")
	viper.SetDefault("review.max_tokens", 80000) // 调整为80000 tokens
	viper.SetDefault("review.enhanced_enabled", true)
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file", "./log/app.log")
	viper.SetDefault("async.enabled", true)
}
