package service

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"path/filepath"
	"regexp"
	"strings"

	"ai-codereview-service/internal/gitlab"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

// RecursiveDependencyAnalyzer 递归依赖分析器
// 支持多级依赖追踪，可以分析2-3层甚至更深的调用链
type RecursiveDependencyAnalyzer struct {
	gitlabClient     *gitlab.Client
	contextExtractor *ContextExtractor
	analyzedFiles    map[string]bool     // 防止循环依赖
	dependencyCache  map[string][]string // 依赖缓存
	projectStructure *ProjectStructure   // 项目结构信息
	projectPackages  []string            // 动态检测到的项目包名前缀
}

// DependencyInfo 依赖信息
type DependencyInfo struct {
	FilePath      string              `json:"file_path"`
	Dependencies  []string            `json:"dependencies"`
	ImportedBy    []string            `json:"imported_by"`
	FunctionCalls map[string][]string `json:"function_calls"`
	Level         int                 `json:"level"` // 依赖层级
}

// DependencyGraph 依赖图
type DependencyGraph struct {
	Nodes    map[string]*DependencyInfo `json:"nodes"`
	Edges    map[string][]string        `json:"edges"`
	MaxDepth int                        `json:"max_depth"`
}

// ProjectStructure 项目结构信息
type ProjectStructure struct {
	IsMultiModule bool              `json:"is_multi_module"`
	Modules       []string          `json:"modules"`
	ModuleMapping map[string]string `json:"module_mapping"` // 包名 -> 模块名映射
	RootPath      string            `json:"root_path"`
}

// NewRecursiveDependencyAnalyzer 创建递归依赖分析器
func NewRecursiveDependencyAnalyzer(gitlabClient *gitlab.Client, contextExtractor *ContextExtractor) *RecursiveDependencyAnalyzer {
	return &RecursiveDependencyAnalyzer{
		gitlabClient:     gitlabClient,
		contextExtractor: contextExtractor,
		analyzedFiles:    make(map[string]bool),
		dependencyCache:  make(map[string][]string),
		projectStructure: &ProjectStructure{
			IsMultiModule: false,
			Modules:       []string{},
			ModuleMapping: make(map[string]string),
			RootPath:      "",
		},
		projectPackages: []string{}, // 初始为空，将动态检测
	}
}

// AnalyzeDependencies 分析多级依赖关系
func (rda *RecursiveDependencyAnalyzer) AnalyzeDependencies(changes []repository.GitLabChange, ref, branchName string, maxDepth int) (map[string][]string, error) {
	logger.Infof("开始多级依赖分析: 最大深度=%d, 变更文件数=%d", maxDepth, len(changes))

	// 重置分析状态
	rda.analyzedFiles = make(map[string]bool)

	// 首先检测项目结构
	if err := rda.detectProjectStructure(changes, branchName); err != nil {
		logger.Warnf("项目结构检测失败，继续使用默认逻辑: %v", err)
	}

	dependencyGraph := &DependencyGraph{
		Nodes:    make(map[string]*DependencyInfo),
		Edges:    make(map[string][]string),
		MaxDepth: maxDepth,
	}

	// 第一层：分析变更文件的直接依赖
	for _, change := range changes {
		filePath := rda.getValidFilePath(change)
		if filePath == "" {
			continue
		}

		logger.Infof("分析文件依赖: %s", filePath)
		err := rda.analyzeFileRecursively(filePath, ref, branchName, 0, maxDepth, dependencyGraph)
		if err != nil {
			logger.Warnf("分析文件依赖失败: %s, 错误: %v", filePath, err)
			continue
		}
	}

	// 转换为简单的依赖映射
	result := make(map[string][]string)
	for filePath, info := range dependencyGraph.Nodes {
		result[filePath] = info.Dependencies
	}

	logger.Infof("依赖分析完成: 分析了%d个文件, 最大深度=%d, 多模块项目=%t",
		len(result), dependencyGraph.MaxDepth, rda.projectStructure.IsMultiModule)

	// 打印项目结构信息
	if rda.projectStructure.IsMultiModule {
		logger.Infof("检测到的模块: %v", rda.projectStructure.Modules)
	}

	return result, nil
}

// analyzeFileRecursively 递归分析文件依赖
func (rda *RecursiveDependencyAnalyzer) analyzeFileRecursively(filePath, ref, branchName string, currentDepth, maxDepth int, graph *DependencyGraph) error {
	// 检查是否已经分析过这个文件（防止循环依赖）
	if rda.analyzedFiles[filePath] {
		return nil
	}

	// 检查深度限制
	if currentDepth >= maxDepth {
		logger.Debugf("达到最大分析深度 %d，停止分析文件: %s", maxDepth, filePath)
		return nil
	}

	// 标记为已分析
	rda.analyzedFiles[filePath] = true

	logger.Debugf("递归分析文件依赖: %s (深度: %d/%d)", filePath, currentDepth, maxDepth)

	// 获取文件内容 - 优先使用ref（commit哈希），fallback到分支名
	fileRef := ref
	if fileRef == "" {
		fileRef = branchName
	}
	fileContent, err := rda.contextExtractor.GetFileContentFromGitLabSimple(filePath, fileRef)
	if err != nil {
		return fmt.Errorf("获取文件内容失败: %w", err)
	}

	// 创建依赖信息节点
	depInfo := &DependencyInfo{
		FilePath:      filePath,
		Dependencies:  []string{},
		ImportedBy:    []string{},
		FunctionCalls: make(map[string][]string),
		Level:         currentDepth,
	}

	// 根据文件类型分析依赖
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".java":
		rda.analyzeJavaDependencies(fileContent, filePath, branchName, depInfo)
	case ".go":
		rda.analyzeGoDependencies(fileContent, filePath, depInfo)
	case ".py":
		rda.analyzePythonDependencies(fileContent, filePath, depInfo)
	case ".js", ".ts":
		rda.analyzeJSDependencies(fileContent, filePath, depInfo)
	case ".xml":
		if strings.Contains(filePath, "pom.xml") {
			rda.analyzePomDependencies(fileContent, filePath, depInfo)
		}
	}

	// 将节点添加到图中
	graph.Nodes[filePath] = depInfo
	graph.Edges[filePath] = depInfo.Dependencies

	// 递归分析依赖文件
	for _, depPath := range depInfo.Dependencies {
		// 直接使用依赖路径，不再进行额外解析
		if depPath != "" && !rda.analyzedFiles[depPath] {
			// 验证文件是否真实存在再进行分析
			if rda.validateFilePath(depPath, ref, branchName) {
				err := rda.analyzeFileRecursively(depPath, ref, branchName, currentDepth+1, maxDepth, graph)
				if err != nil {
					logger.Debugf("递归分析依赖文件失败: %s -> %s, 错误: %v", filePath, depPath, err)
				}
			} else {
				logger.Debugf("跳过不存在的依赖文件: %s", depPath)
			}
		}
	}

	return nil
}

// analyzeJavaDependencies 分析Java文件依赖 - 重写路径处理逻辑
func (rda *RecursiveDependencyAnalyzer) analyzeJavaDependencies(fileContent, filePath, branchName string, depInfo *DependencyInfo) {
	// 在开始分析之前，先检测项目包名结构
	if len(rda.projectPackages) == 0 {
		// 从当前文件路径推断项目包名
		if packagePrefix := rda.extractPackageFromPath(filePath); packagePrefix != "" {
			rda.projectPackages = append(rda.projectPackages, packagePrefix)
			logger.Infof("从文件路径推断项目包名: %s", packagePrefix)
		}
	}

	lines := strings.Split(fileContent, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 分析import语句
		if strings.HasPrefix(line, "import ") {
			importPath := rda.extractJavaImport(line)
			if importPath != "" {
				// 🔥 关键修复：严格过滤外部依赖和跨项目依赖
				if rda.isExternalDependency(importPath) {
					logger.Debugf("跳过外部依赖: %s", importPath)
					continue
				}

				if rda.isCrossProjectDependency(importPath) {
					logger.Debugf("跳过跨项目依赖: %s", importPath)
					continue
				}

				// 🔥 关键修复：只处理当前项目内部类
				if rda.isCurrentProjectClass(importPath) {
					// 转换为实际文件路径
					actualFilePath := rda.resolveImportToValidPath(importPath, branchName)
					if actualFilePath != "" {
						depInfo.Dependencies = append(depInfo.Dependencies, actualFilePath)
						logger.Debugf("添加内部依赖: %s -> %s", importPath, actualFilePath)
					} else {
						logger.Debugf("无法解析内部依赖路径: %s", importPath)
					}
				} else {
					logger.Debugf("跳过非项目类: %s", importPath)
				}
			}
		}

		// 分析方法调用
		rda.extractJavaMethodCalls(line, depInfo)
	}
}

// resolveImportToValidPath 将import路径解析为有效的文件路径 - 重写逻辑
func (rda *RecursiveDependencyAnalyzer) resolveImportToValidPath(importPath, branchName string) string {
	// 将包名转换为相对Java源码路径
	pathParts := strings.Split(importPath, ".")
	javaFileName := pathParts[len(pathParts)-1] + ".java"
	packagePath := strings.Join(pathParts[:len(pathParts)-1], "/")
	relativeJavaPath := fmt.Sprintf("src/main/java/%s/%s", packagePath, javaFileName)

	// 生成候选路径
	var candidatePaths []string

	// 如果是多模块项目，为每个模块生成路径
	if rda.projectStructure.IsMultiModule && len(rda.projectStructure.Modules) > 0 {
		for _, module := range rda.projectStructure.Modules {
			fullPath := fmt.Sprintf("%s/%s", module, relativeJavaPath)
			candidatePaths = append(candidatePaths, fullPath)
		}
	} else {
		// 单模块项目或默认情况
		candidatePaths = append(candidatePaths, relativeJavaPath)
	}

	// 尝试每个候选路径，返回第一个存在的
	for _, path := range candidatePaths {
		if rda.validateFilePath(path, "", branchName) {
			logger.Debugf("找到有效路径: %s -> %s", importPath, path)
			return path
		}
	}

	logger.Debugf("未找到有效路径: %s (尝试了 %d 个候选路径)", importPath, len(candidatePaths))
	return ""
}

// analyzeGoDependencies 分析Go文件依赖
func (rda *RecursiveDependencyAnalyzer) analyzeGoDependencies(fileContent, filePath string, depInfo *DependencyInfo) {
	// 使用Go的AST解析器进行精确分析
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, "", fileContent, parser.ParseComments)
	if err != nil {
		logger.Warnf("Go文件AST解析失败: %v", err)
		return
	}

	// 提取import依赖
	for _, imp := range node.Imports {
		importPath := strings.Trim(imp.Path.Value, `"`)

		// 🔥 关键修复：为Go语言添加外部依赖过滤
		if rda.isGoExternalDependency(importPath) {
			logger.Debugf("跳过Go外部依赖: %s", importPath)
			continue
		}

		// 只处理项目内部的Go模块
		if rda.isGoInternalModule(importPath) {
			depInfo.Dependencies = append(depInfo.Dependencies, importPath)
			logger.Debugf("添加Go内部依赖: %s", importPath)
		} else {
			logger.Debugf("跳过非项目Go模块: %s", importPath)
		}
	}

	// 分析函数调用
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.CallExpr:
			if ident, ok := x.Fun.(*ast.Ident); ok {
				if depInfo.FunctionCalls[filePath] == nil {
					depInfo.FunctionCalls[filePath] = []string{}
				}
				depInfo.FunctionCalls[filePath] = append(depInfo.FunctionCalls[filePath], ident.Name)
			}
		}
		return true
	})
}

// analyzePythonDependencies 分析Python文件依赖
func (rda *RecursiveDependencyAnalyzer) analyzePythonDependencies(fileContent, filePath string, depInfo *DependencyInfo) {
	lines := strings.Split(fileContent, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 分析import语句
		if strings.HasPrefix(line, "import ") || strings.HasPrefix(line, "from ") {
			importPath := rda.extractPythonImport(line)
			if importPath != "" {
				depInfo.Dependencies = append(depInfo.Dependencies, importPath)
			}
		}
	}
}

// analyzeJSDependencies 分析JavaScript/TypeScript文件依赖
func (rda *RecursiveDependencyAnalyzer) analyzeJSDependencies(fileContent, filePath string, depInfo *DependencyInfo) {
	// 正则表达式匹配import和require语句
	importPatterns := []string{
		`import\s+.*?\s+from\s+['"]([^'"]+)['"]`,
		`import\s+['"]([^'"]+)['"]`,
		`require\(['"]([^'"]+)['"]\)`,
	}

	for _, pattern := range importPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllStringSubmatch(fileContent, -1)
		for _, match := range matches {
			if len(match) > 1 {
				depInfo.Dependencies = append(depInfo.Dependencies, match[1])
			}
		}
	}
}

// analyzePomDependencies 分析pom.xml依赖
func (rda *RecursiveDependencyAnalyzer) analyzePomDependencies(fileContent, filePath string, depInfo *DependencyInfo) {
	// 使用正则表达式解析Maven依赖
	dependencyRegex := regexp.MustCompile(`<groupId>(.*?)</groupId>\s*<artifactId>(.*?)</artifactId>`)
	matches := dependencyRegex.FindAllStringSubmatch(fileContent, -1)

	for _, match := range matches {
		if len(match) >= 3 {
			dependency := fmt.Sprintf("%s:%s", match[1], match[2])
			depInfo.Dependencies = append(depInfo.Dependencies, dependency)
		}
	}
}

// 辅助方法
func (rda *RecursiveDependencyAnalyzer) getValidFilePath(change repository.GitLabChange) string {
	if change.DeletedFile {
		return "" // 跳过删除的文件
	}
	if change.NewPath != "" {
		return change.NewPath
	}
	return change.OldPath
}

func (rda *RecursiveDependencyAnalyzer) extractJavaImport(line string) string {
	// 匹配import语句
	re := regexp.MustCompile(`import\s+(static\s+)?([a-zA-Z_][a-zA-Z0-9_.]*(\*)?);`)
	matches := re.FindStringSubmatch(strings.TrimSpace(line))
	if len(matches) > 2 {
		importPath := matches[2]
		// 移除静态导入的方法部分，只保留类路径
		if strings.Contains(importPath, "*") {
			return "" // 通配符导入通常是工具类，跳过
		}
		return strings.TrimSuffix(importPath, ".*")
	}
	return ""
}

// isExternalDependency 判断是否为外部依赖 - 完全重写逻辑
func (rda *RecursiveDependencyAnalyzer) isExternalDependency(importPath string) bool {
	// 首先判断是否为当前项目内部类
	if rda.isCurrentProjectClass(importPath) {
		return false
	}

	// 扩展的外部依赖库列表
	externalPrefixes := []string{
		// JDK和标准库
		"java.", "javax.", "sun.", "com.sun.", "jdk.",

		// Spring生态系统
		"org.springframework.", "org.springframework.boot.", "org.springframework.data.",
		"org.springframework.security.", "org.springframework.cloud.", "org.springframework.web.",

		// JSON处理
		"com.alibaba.fastjson.", "com.fasterxml.jackson.", "com.google.gson.",

		// 实用工具库
		"lombok.", "cn.hutool.", "org.apache.commons.", "com.google.guava.",

		// 日志框架
		"org.slf4j.", "ch.qos.logback.", "org.apache.logging.log4j.",

		// 数据库相关
		"com.baomidou.", "org.mybatis.", "org.hibernate.", "javax.persistence.",

		// HTTP客户端
		"org.apache.http.", "okhttp3.", "com.squareup.okhttp3.",

		// 缓存和Redis
		"org.redisson.", "redis.clients.jedis.", "org.springframework.data.redis.",

		// 消息队列
		"org.apache.kafka.", "org.springframework.amqp.", "com.rabbitmq.",

		// 监控和度量
		"com.dianping.cat.", "io.micrometer.", "org.springframework.boot.actuator.",

		// Apollo配置中心
		"com.ctrip.framework.apollo.", "com.ctrip.framework.foundation.",

		// 分布式追踪
		"io.zipkin.", "brave.", "org.springframework.cloud.sleuth.",

		// 测试框架
		"org.junit.", "org.testng.", "org.mockito.", "org.powermock.",

		// Swagger/OpenAPI
		"io.swagger.", "org.springdoc.",

		// 序列化
		"org.apache.avro.", "com.esotericsoftware.kryo.",

		// 时间处理
		"org.joda.time.", "java.time.",
	}

	for _, prefix := range externalPrefixes {
		if strings.HasPrefix(importPath, prefix) {
			return true
		}
	}

	return false
}

// isCurrentProjectClass 判断是否为当前项目内部类 - 重写逻辑
func (rda *RecursiveDependencyAnalyzer) isCurrentProjectClass(className string) bool {
	// 如果还没有检测到项目包名，尝试检测
	if len(rda.projectPackages) == 0 {
		return false
	}

	// 检查是否匹配任何项目包名前缀
	for _, packagePrefix := range rda.projectPackages {
		if strings.HasPrefix(className, packagePrefix) {
			return true
		}
	}

	return false
}

// isCrossProjectDependency 判断是否为跨项目依赖 - 完全重写逻辑
func (rda *RecursiveDependencyAnalyzer) isCrossProjectDependency(importPath string) bool {
	// 跨项目依赖的包名前缀
	crossProjectPrefixes := []string{
		// 公司平台服务
		"com.yupaopao.platform.",
		"com.yupaopao.framework.",
		"com.yupaopao.common.",
		"com.yupaopao.sdk.",
		"com.yupaopao.infrastructure.",

		// 其他公司项目（如果有的话）
		"com.bixin.platform.",
		"com.bixin.framework.",
		"com.bixin.common.",
	}

	for _, prefix := range crossProjectPrefixes {
		if strings.HasPrefix(importPath, prefix) {
			return true
		}
	}

	return false
}

func (rda *RecursiveDependencyAnalyzer) extractPythonImport(line string) string {
	if strings.HasPrefix(line, "from ") {
		// from module import something
		parts := strings.Split(line, " import ")
		if len(parts) > 0 {
			return strings.TrimPrefix(parts[0], "from ")
		}
	} else if strings.HasPrefix(line, "import ") {
		// import module
		return strings.TrimPrefix(line, "import ")
	}
	return ""
}

func (rda *RecursiveDependencyAnalyzer) extractJavaMethodCalls(line string, depInfo *DependencyInfo) {
	// 简单的方法调用模式匹配
	methodCallRegex := regexp.MustCompile(`(\w+)\.(\w+)\s*\(`)
	matches := methodCallRegex.FindAllStringSubmatch(line, -1)

	for _, match := range matches {
		if len(match) >= 3 {
			objectName := match[1]
			methodName := match[2]

			if depInfo.FunctionCalls[depInfo.FilePath] == nil {
				depInfo.FunctionCalls[depInfo.FilePath] = []string{}
			}
			depInfo.FunctionCalls[depInfo.FilePath] = append(
				depInfo.FunctionCalls[depInfo.FilePath],
				fmt.Sprintf("%s.%s", objectName, methodName))
		}
	}
}

// validateFilePath 验证文件路径是否真实存在 - 简化逻辑
func (rda *RecursiveDependencyAnalyzer) validateFilePath(filePath, ref, branchName string) bool {
	// 优先使用ref（commit哈希），fallback到分支名
	fileRef := ref
	if fileRef == "" {
		fileRef = branchName
	}

	// 尝试获取文件内容来验证文件是否存在
	_, err := rda.contextExtractor.GetFileContentFromGitLabSimple(filePath, fileRef)
	return err == nil
}

// detectProjectStructure 检测项目结构 - 重写逻辑
func (rda *RecursiveDependencyAnalyzer) detectProjectStructure(changes []repository.GitLabChange, branchName string) error {
	logger.Infof("开始检测项目结构...")

	// 重置状态
	rda.projectStructure = &ProjectStructure{
		IsMultiModule: false,
		Modules:       []string{},
		ModuleMapping: make(map[string]string),
		RootPath:      "",
	}

	// 检查是否存在根pom.xml
	if _, err := rda.contextExtractor.GetFileContentFromGitLabSimple("pom.xml", branchName); err != nil {
		logger.Infof("非Maven项目或无根pom.xml")
		return nil
	}

	// 检测实际存在的模块
	detectedModules := rda.detectProjectModules(branchName)

	if len(detectedModules) > 0 {
		rda.projectStructure.IsMultiModule = true
		rda.projectStructure.Modules = detectedModules
		logger.Infof("检测到多模块项目，模块数量: %d, 模块列表: %v", len(detectedModules), detectedModules)
	} else {
		logger.Infof("检测到单模块项目")
	}

	// 检测项目包名
	if err := rda.detectProjectPackages(changes, branchName); err != nil {
		logger.Warnf("项目包名检测失败: %v", err)
	}

	return nil
}

// detectProjectModules 检测实际存在的项目模块 - 完全重写，移除硬编码
func (rda *RecursiveDependencyAnalyzer) detectProjectModules(branchName string) []string {
	var modules []string

	// 🔥 第一步：获取项目根目录结构，动态发现所有目录
	tree, err := rda.gitlabClient.ListRepositoryTree(branchName, "")
	if err != nil {
		logger.Warnf("无法获取项目根目录结构: %v", err)
		return modules
	}

	// 🔥 第二步：遍历所有顶级目录，检查是否为有效模块
	for _, item := range tree {
		if path, ok := item["path"].(string); ok {
			if itemType, ok := item["type"].(string); ok && itemType == "tree" {
				// 检查每个目录是否为有效模块
				if rda.isValidModule(path, branchName) {
					modules = append(modules, path)
					logger.Debugf("找到有效模块: %s", path)
				}
			}
		}
	}

	// 🔥 第三步：如果没有找到模块，可能是单模块项目
	if len(modules) == 0 {
		// 检查根目录是否直接包含src/main/java结构
		if tree, err := rda.gitlabClient.ListRepositoryTree(branchName, "src/main/java"); err == nil && len(tree) > 0 {
			logger.Infof("检测到单模块项目结构（根目录包含src/main/java）")
		}
	}

	logger.Infof("模块检测完成，共发现 %d 个模块: %v", len(modules), modules)
	return modules
}

// isValidModule 检查模块是否有效 - 增强逻辑
func (rda *RecursiveDependencyAnalyzer) isValidModule(modulePath, branchName string) bool {
	// 🔥 方法1：检查模块目录下是否存在pom.xml（Maven模块）
	pomPath := fmt.Sprintf("%s/pom.xml", modulePath)
	if _, err := rda.contextExtractor.GetFileContentFromGitLabSimple(pomPath, branchName); err == nil {
		logger.Debugf("发现Maven模块: %s (包含pom.xml)", modulePath)
		return true
	}

	// 🔥 方法2：检查是否存在src/main/java目录结构（Java项目）
	srcPath := fmt.Sprintf("%s/src/main/java", modulePath)
	if tree, err := rda.gitlabClient.ListRepositoryTree(branchName, srcPath); err == nil && len(tree) > 0 {
		logger.Debugf("发现Java模块: %s (包含src/main/java)", modulePath)
		return true
	}

	// 🔥 方法3：检查是否存在src/main/resources目录（资源模块）
	resourcePath := fmt.Sprintf("%s/src/main/resources", modulePath)
	if tree, err := rda.gitlabClient.ListRepositoryTree(branchName, resourcePath); err == nil && len(tree) > 0 {
		logger.Debugf("发现资源模块: %s (包含src/main/resources)", modulePath)
		return true
	}

	// 🔥 方法4：检查是否存在build.gradle（Gradle模块）
	gradlePath := fmt.Sprintf("%s/build.gradle", modulePath)
	if _, err := rda.contextExtractor.GetFileContentFromGitLabSimple(gradlePath, branchName); err == nil {
		logger.Debugf("发现Gradle模块: %s (包含build.gradle)", modulePath)
		return true
	}

	return false
}

// detectProjectPackages 动态检测项目的包名结构 - 重写逻辑
func (rda *RecursiveDependencyAnalyzer) detectProjectPackages(changes []repository.GitLabChange, branchName string) error {
	logger.Infof("开始动态检测项目包名结构...")

	var detectedPackages []string
	packageSet := make(map[string]bool)

	// 分析变更文件的路径来推断项目包名
	for _, change := range changes {
		filePath := rda.getValidFilePath(change)
		if filePath == "" || !strings.HasSuffix(filePath, ".java") {
			continue
		}

		// 从Java文件路径推断包名
		if packagePrefix := rda.extractPackageFromPath(filePath); packagePrefix != "" {
			if !packageSet[packagePrefix] {
				packageSet[packagePrefix] = true
				detectedPackages = append(detectedPackages, packagePrefix)
				logger.Debugf("检测到项目包名: %s", packagePrefix)
			}
		}
	}

	// 如果没有检测到包名，尝试从项目结构推断
	if len(detectedPackages) == 0 {
		detectedPackages = rda.inferPackagesFromProjectStructure(branchName)
	}

	if len(detectedPackages) > 0 {
		rda.projectPackages = detectedPackages
		logger.Infof("成功检测到 %d 个项目包名前缀: %v", len(detectedPackages), detectedPackages)
	} else {
		logger.Warnf("未能检测到项目包名，将使用保守的外部依赖识别策略")
	}

	return nil
}

// extractPackageFromPath 从Java文件路径中提取包名前缀 - 完全重写，移除硬编码
func (rda *RecursiveDependencyAnalyzer) extractPackageFromPath(filePath string) string {
	// 查找 src/main/java/ 模式
	javaPathIndex := strings.Index(filePath, "/src/main/java/")
	if javaPathIndex == -1 {
		return ""
	}

	// 提取 src/main/java/ 之后的路径
	packagePath := filePath[javaPathIndex+len("/src/main/java/"):]

	// 移除文件名，只保留包路径
	lastSlashIndex := strings.LastIndex(packagePath, "/")
	if lastSlashIndex == -1 {
		return ""
	}
	packagePath = packagePath[:lastSlashIndex]

	// 转换为Java包名格式
	javaPackage := strings.ReplaceAll(packagePath, "/", ".")

	// 🔥 智能提取包名前缀 - 不再硬编码公司名
	parts := strings.Split(javaPackage, ".")

	// 根据包名层数智能判断
	if len(parts) >= 4 {
		// 4层或以上：com.company.project.module -> com.company.project.module
		return strings.Join(parts[:4], ".")
	} else if len(parts) >= 3 {
		// 3层：com.company.project -> com.company.project
		return strings.Join(parts[:3], ".")
	} else if len(parts) >= 2 {
		// 2层：com.company -> com.company
		return strings.Join(parts[:2], ".")
	}

	return ""
}

// inferPackagesFromProjectStructure 从项目结构推断包名 - 重写逻辑
func (rda *RecursiveDependencyAnalyzer) inferPackagesFromProjectStructure(branchName string) []string {
	var packages []string

	// 尝试从主要模块的Java文件推断
	for _, module := range rda.projectStructure.Modules {
		javaPath := fmt.Sprintf("%s/src/main/java", module)
		if tree, err := rda.gitlabClient.ListRepositoryTree(branchName, javaPath); err == nil {
			if packagePrefix := rda.extractPackageFromTree(tree, ""); packagePrefix != "" {
				packages = append(packages, packagePrefix)
				logger.Debugf("从模块 %s 推断出包名: %s", module, packagePrefix)
			}
		}
	}

	// 如果模块检测失败，尝试直接从src/main/java推断
	if len(packages) == 0 {
		if tree, err := rda.gitlabClient.ListRepositoryTree(branchName, "src/main/java"); err == nil {
			if packagePrefix := rda.extractPackageFromTree(tree, ""); packagePrefix != "" {
				packages = append(packages, packagePrefix)
				logger.Debugf("从根目录推断出包名: %s", packagePrefix)
			}
		}
	}

	return packages
}

// extractPackageFromTree 从目录树中提取包名 - 完全重写，移除硬编码
func (rda *RecursiveDependencyAnalyzer) extractPackageFromTree(tree []map[string]interface{}, basePath string) string {
	for _, item := range tree {
		if path, ok := item["path"].(string); ok {
			if itemType, ok := item["type"].(string); ok && itemType == "tree" {
				fullPath := path
				if basePath != "" {
					fullPath = basePath + "." + path
				}

				// 当路径达到合理层数时，认为是一个合理的包前缀
				parts := strings.Split(fullPath, ".")
				if len(parts) >= 3 {
					// 🔥 移除硬编码检查，基于通用规则
					if len(parts) >= 4 {
						// 4层包名，通常是完整的项目包名
						return fullPath
					} else if len(parts) == 3 {
						// 3层包名也是合理的
						return fullPath
					}
				}

				// 递归查找更深层的包结构（限制递归深度防止无限递归）
				if len(parts) < 6 {
					subPath := fmt.Sprintf("src/main/java/%s", strings.ReplaceAll(fullPath, ".", "/"))
					if subTree, err := rda.gitlabClient.ListRepositoryTree("", subPath); err == nil {
						if subPackage := rda.extractPackageFromTree(subTree, fullPath); subPackage != "" {
							return subPackage
						}
					}
				}
			}
		}
	}
	return ""
}

// isGoExternalDependency 判断是否为Go语言的外部依赖
func (rda *RecursiveDependencyAnalyzer) isGoExternalDependency(importPath string) bool {
	// Go标准库
	standardLibs := []string{
		"fmt", "io", "os", "log", "time", "strings", "strconv", "regexp",
		"net", "net/http", "net/url", "context", "sync", "errors",
		"path", "path/filepath", "encoding/json", "encoding/xml",
		"database/sql", "crypto", "hash", "sort", "math", "reflect",
		"runtime", "testing", "flag", "bufio", "bytes", "compress",
	}

	// 检查是否为标准库
	for _, lib := range standardLibs {
		if importPath == lib || strings.HasPrefix(importPath, lib+"/") {
			return true
		}
	}

	// 第三方库（以域名开头的包）
	externalPrefixes := []string{
		"github.com/", "golang.org/", "google.golang.org/",
		"gopkg.in/", "go.uber.org/", "go.mongodb.org/",
		"cloud.google.com/", "firebase.google.com/",
		"k8s.io/", "sigs.k8s.io/", "istio.io/",
	}

	for _, prefix := range externalPrefixes {
		if strings.HasPrefix(importPath, prefix) {
			return true
		}
	}

	return false
}

// isGoInternalModule 判断是否为Go语言的项目内部模块
func (rda *RecursiveDependencyAnalyzer) isGoInternalModule(importPath string) bool {
	// 检查是否以项目模块名开头
	if strings.HasPrefix(importPath, "ai-codereview-service/") {
		return true
	}

	// 相对导入（通常是内部模块）
	if strings.HasPrefix(importPath, "./") || strings.HasPrefix(importPath, "../") {
		return true
	}

	// 如果不是外部依赖且不是明显的第三方库，可能是内部模块
	// 这里采用保守策略：只有明确是项目内部的才处理
	return false
}
