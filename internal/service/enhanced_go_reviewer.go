package service

import (
	"fmt"
	"regexp"
	"strings"
	"sync"

	"ai-codereview-service/pkg/logger"
)

// GoReviewConfig Go代码审查配置
type GoReviewConfig struct {
	MaxFunctionLines  int // 函数最大行数
	MaxMethodLines    int // 方法最大行数
	MaxParameters     int // 最大参数个数
	MaxGlobalVars     int // 最大全局变量数
	MaxTraversalDepth int // 最大遍历深度
}

// DefaultGoReviewConfig 默认配置
func DefaultGoReviewConfig() *GoReviewConfig {
	return &GoReviewConfig{
		MaxFunctionLines:  50,
		MaxMethodLines:    50,
		MaxParameters:     5,
		MaxGlobalVars:     1000,
		MaxTraversalDepth: 50,
	}
}

// EnhancedGoReviewer 增强的Go审查器，集成Tree-sitter分析
type EnhancedGoReviewer struct {
	config       *GoReviewConfig
	analyzerPool sync.Pool // 使用对象池管理analyzer实例
}

// NewEnhancedGoReviewer 创建增强的Go审查器
func NewEnhancedGoReviewer() *EnhancedGoReviewer {
	reviewer := &EnhancedGoReviewer{
		config: DefaultGoReviewConfig(),
	}
	reviewer.analyzerPool.New = func() interface{} {
		return NewGoTreeSitterAnalyzer()
	}
	return reviewer
}

// NewEnhancedGoReviewerWithConfig 创建带自定义配置的增强Go审查器
func NewEnhancedGoReviewerWithConfig(config *GoReviewConfig) *EnhancedGoReviewer {
	if config == nil {
		config = DefaultGoReviewConfig()
	}
	reviewer := &EnhancedGoReviewer{
		config: config,
	}
	reviewer.analyzerPool.New = func() interface{} {
		return NewGoTreeSitterAnalyzer()
	}
	return reviewer
}

// ReviewGoCode 审查Go代码，结合Tree-sitter分析和Go语言最佳实践检查
func (reviewer *EnhancedGoReviewer) ReviewGoCode(code string, fileName string) (*GoCodeReviewResult, error) {
	logger.Infof("开始使用Tree-sitter对Go代码进行增强审查: %s", fileName)

	// 从对象池获取analyzer实例
	analyzer := reviewer.analyzerPool.Get().(*GoTreeSitterAnalyzer)
	defer reviewer.analyzerPool.Put(analyzer)

	// 使用Tree-sitter进行深度分析
	analysisResult, err := analyzer.AnalyzeGoCode(code)
	if err != nil {
		logger.Warnf("Tree-sitter分析失败，回退到传统方法: %v", err)
		fallbackResult, fallbackErr := reviewer.fallbackReview(code, fileName)
		if fallbackErr != nil {
			return nil, fmt.Errorf("Tree-sitter分析失败且回退方法也失败: %v, %v", err, fallbackErr)
		}
		return fallbackResult, nil
	}

	// 构建审查结果
	result := &GoCodeReviewResult{
		FileName:          fileName,
		TreeSitterResult:  analysisResult,
		CodeSmells:        []string{},
		PerformanceIssues: []string{},
		SecurityIssues:    []string{},
		BestPractices:     []string{},
		QualityScore:      85, // 基础分数
	}

	// 基于Tree-sitter分析结果进行具体审查
	reviewer.analyzeCodeSmells(result)
	reviewer.analyzePerformanceIssues(result)
	reviewer.analyzeSecurityIssues(result)
	reviewer.analyzeBestPractices(result)

	// 计算质量分数
	reviewer.calculateQualityScore(result)

	logger.Infof("Go代码审查完成: %s", fileName)

	return result, nil
}

// analyzeCodeSmells 分析Go代码异味
func (reviewer *EnhancedGoReviewer) analyzeCodeSmells(result *GoCodeReviewResult) {
	if result.TreeSitterResult == nil {
		return
	}

	// 1. 长函数检测
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.isFunctionTooLong(function) {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("长函数警告: %s函数过长，建议拆分", function.Name))
		}
	}

	// 2. 长方法检测
	for _, method := range result.TreeSitterResult.Methods {
		if reviewer.isMethodTooLong(method) {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("长方法警告: %s方法过长，建议拆分", method.Name))
		}
	}

	// 3. 参数过多检测
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.hasTooManyParameters(function) {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("参数过多: %s函数参数过多，建议使用结构体传参", function.Name))
		}
	}

	// 4. 空接口使用检测
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.usesEmptyInterface(function) {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("空接口使用: %s使用了interface{}，建议使用具体类型", function.Name))
		}
	}
}

// analyzePerformanceIssues 分析Go性能问题
func (reviewer *EnhancedGoReviewer) analyzePerformanceIssues(result *GoCodeReviewResult) {
	if result.TreeSitterResult == nil {
		return
	}

	// 1. Goroutine泄漏
	result.PerformanceIssues = append(result.PerformanceIssues, result.TreeSitterResult.GoroutineLeaks...)

	// 2. 数据竞争
	result.PerformanceIssues = append(result.PerformanceIssues, result.TreeSitterResult.RaceConditions...)

	// 3. 内存泄漏
	result.PerformanceIssues = append(result.PerformanceIssues, result.TreeSitterResult.MemoryLeaks...)

	// 4. 字符串拼接性能问题
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.hasStringConcatenationIssue(function) {
			result.PerformanceIssues = append(result.PerformanceIssues,
				fmt.Sprintf("字符串拼接性能: %s函数中存在多次字符串拼接，建议使用strings.Builder", function.Name))
		}
	}

	// 5. 切片预分配建议
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.needsSlicePreallocation(function) {
			result.PerformanceIssues = append(result.PerformanceIssues,
				fmt.Sprintf("切片优化: %s函数中的切片建议预分配容量", function.Name))
		}
	}
}

// analyzeSecurityIssues 分析Go安全问题
func (reviewer *EnhancedGoReviewer) analyzeSecurityIssues(result *GoCodeReviewResult) {
	if result.TreeSitterResult == nil {
		return
	}

	// 1. SQL注入风险
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.hasSQLInjectionRisk(function) {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("SQL注入风险: %s函数中可能存在SQL注入漏洞", function.Name))
		}
	}

	// 2. 路径遍历攻击
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.hasPathTraversalRisk(function) {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("路径遍历风险: %s函数中可能存在路径遍历攻击风险", function.Name))
		}
	}

	// 3. 敏感信息泄露
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.hasInfoLeakage(function) {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("信息泄露: %s函数可能泄露敏感信息", function.Name))
		}
	}
}

// analyzeBestPractices 分析Go最佳实践
func (reviewer *EnhancedGoReviewer) analyzeBestPractices(result *GoCodeReviewResult) {
	if result.TreeSitterResult == nil {
		return
	}

	// 1. 错误处理检查
	result.BestPractices = append(result.BestPractices, result.TreeSitterResult.ErrorHandling...)

	// 2. 接口设计检查
	for _, structInfo := range result.TreeSitterResult.Structs {
		if reviewer.needsInterface(structInfo) {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("接口设计: %s结构体建议抽象为接口", structInfo.Name))
		}
	}

	// 3. 命名规范检查
	for _, function := range result.TreeSitterResult.Functions {
		if !reviewer.followsNamingConvention(function.Name) {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("命名规范: %s函数命名不符合Go命名规范", function.Name))
		}
	}

	// 4. 注释覆盖率检查
	for _, function := range result.TreeSitterResult.Functions {
		if reviewer.needsComment(function) {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("注释缺失: %s函数需要添加注释说明", function.Name))
		}
	}
}

// calculateQualityScore 计算代码质量分数
func (reviewer *EnhancedGoReviewer) calculateQualityScore(result *GoCodeReviewResult) {
	score := result.QualityScore

	// 根据问题数量扣分
	score -= len(result.CodeSmells) * 5
	score -= len(result.PerformanceIssues) * 8
	score -= len(result.SecurityIssues) * 10
	score -= len(result.BestPractices) * 3

	// 确保分数在合理范围内
	if score < 0 {
		score = 0
	} else if score > 100 {
		score = 100
	}

	result.QualityScore = score
}

// fallbackReview 回退到传统审查方法
func (reviewer *EnhancedGoReviewer) fallbackReview(code string, fileName string) (*GoCodeReviewResult, error) {
	result := &GoCodeReviewResult{
		FileName:          fileName,
		TreeSitterResult:  nil,
		CodeSmells:        []string{},
		PerformanceIssues: []string{},
		SecurityIssues:    []string{},
		BestPractices:     []string{},
		QualityScore:      60, // 降低基础分数
	}

	// 使用简单的正则表达式进行基础检查
	reviewer.basicGoAnalysis(code, result)

	return result, nil
}

// basicGoAnalysis 基础Go代码分析
func (reviewer *EnhancedGoReviewer) basicGoAnalysis(code string, result *GoCodeReviewResult) {
	lines := strings.Split(code, "\n")

	// 预编译正则表达式以提高性能
	var (
		errAssignRegex   = regexp.MustCompile(`, err :=`)
		sprintfPlusRegex = regexp.MustCompile(`fmt\.Sprintf.*\+`)
		execCmdRegex     = regexp.MustCompile(`exec\.Command`)
	)

	for i, line := range lines {
		lineNum := i + 1
		line = strings.TrimSpace(line)

		// 使用预编译的正则表达式进行检查
		if errAssignRegex.MatchString(line) && !reviewer.hasSubsequentErrorCheck(lines, i) {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("错误处理: 第%d行的错误未进行检查", lineNum))
		}

		if sprintfPlusRegex.MatchString(line) {
			result.PerformanceIssues = append(result.PerformanceIssues,
				fmt.Sprintf("字符串拼接: 第%d行建议使用strings.Builder", lineNum))
		}

		if execCmdRegex.MatchString(line) {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("命令执行: 第%d行执行外部命令可能存在安全风险", lineNum))
		}
	}
}

// isCodeTooLong 检查代码是否过长的公共方法
func (reviewer *EnhancedGoReviewer) isCodeTooLong(body string, maxLines int) bool {
	return len(strings.Split(body, "\n")) > maxLines
}

func (reviewer *EnhancedGoReviewer) isFunctionTooLong(function MethodInfo) bool {
	return reviewer.isCodeTooLong(function.Body, reviewer.config.MaxFunctionLines)
}

func (reviewer *EnhancedGoReviewer) isMethodTooLong(method MethodInfo) bool {
	return reviewer.isCodeTooLong(method.Body, reviewer.config.MaxMethodLines)
}

func (reviewer *EnhancedGoReviewer) hasTooManyParameters(function MethodInfo) bool {
	return len(function.Parameters) > reviewer.config.MaxParameters
}

func (reviewer *EnhancedGoReviewer) usesEmptyInterface(function MethodInfo) bool {
	return strings.Contains(function.Body, "interface{}")
}

func (reviewer *EnhancedGoReviewer) hasStringConcatenationIssue(function MethodInfo) bool {
	// 检查是否在循环中进行字符串拼接
	body := function.Body
	return strings.Contains(body, "for") && strings.Contains(body, "+") && strings.Count(body, "+") > 3
}

func (reviewer *EnhancedGoReviewer) needsSlicePreallocation(function MethodInfo) bool {
	// 检查是否有append操作但没有预分配
	body := function.Body
	return strings.Contains(body, "append(") && !strings.Contains(body, "make(")
}

func (reviewer *EnhancedGoReviewer) hasSQLInjectionRisk(function MethodInfo) bool {
	body := function.Body
	return (strings.Contains(body, "db.Query") || strings.Contains(body, "db.Exec")) &&
		strings.Contains(body, "fmt.Sprintf")
}

func (reviewer *EnhancedGoReviewer) hasPathTraversalRisk(function MethodInfo) bool {
	body := function.Body
	return strings.Contains(body, "filepath.Join") && strings.Contains(body, "..")
}

func (reviewer *EnhancedGoReviewer) hasInfoLeakage(function MethodInfo) bool {
	body := function.Body
	return strings.Contains(body, "log.Print") &&
		(strings.Contains(body, "password") || strings.Contains(body, "token"))
}

func (reviewer *EnhancedGoReviewer) needsInterface(structInfo ClassInfo) bool {
	name := structInfo.Name

	// 基于命名规范的基础判断
	hasServicePattern := strings.Contains(name, "Service") ||
		strings.Contains(name, "Manager") ||
		strings.Contains(name, "Repository") ||
		strings.Contains(name, "Handler") ||
		strings.Contains(name, "Client")

	// TODO: 可以进一步扩展，结合结构体方法数量、复杂度等因素
	// 目前先基于命名规范，后续可以添加更多判断逻辑
	return hasServicePattern
}

func (reviewer *EnhancedGoReviewer) followsNamingConvention(funcName string) bool {
	// Go命名规范：首字母大写为公开，小写为私有
	if len(funcName) == 0 {
		return false
	}

	// 检查是否为驼峰命名
	return !strings.Contains(funcName, "_") && !strings.Contains(funcName, "-")
}

func (reviewer *EnhancedGoReviewer) needsComment(function MethodInfo) bool {
	// 公开函数需要注释
	if len(function.Name) > 0 && function.Name[0] >= 'A' && function.Name[0] <= 'Z' {
		// 检查函数体前是否有注释（简化版）
		return !strings.Contains(function.Body, "//")
	}
	return false
}

func (reviewer *EnhancedGoReviewer) hasSubsequentErrorCheck(lines []string, currentIndex int) bool {
	// 检查后续几行是否有错误处理
	for i := currentIndex + 1; i < len(lines) && i < currentIndex+5; i++ {
		line := strings.TrimSpace(lines[i])
		if strings.Contains(line, "if err != nil") {
			return true
		}
	}
	return false
}

// GoCodeReviewResult Go代码审查结果
type GoCodeReviewResult struct {
	FileName          string            `json:"file_name"`
	TreeSitterResult  *GoAnalysisResult `json:"tree_sitter_result,omitempty"`
	CodeSmells        []string          `json:"code_smells"`
	PerformanceIssues []string          `json:"performance_issues"`
	SecurityIssues    []string          `json:"security_issues"`
	BestPractices     []string          `json:"best_practices"`
	QualityScore      int               `json:"quality_score"`
}
