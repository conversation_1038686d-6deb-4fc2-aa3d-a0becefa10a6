package service

import (
	"fmt"
	"strings"

	"ai-codereview-service/pkg/logger"
)

// EnhancedJavaScriptReviewer 增强的JavaScript审查器，集成Tree-sitter分析
type EnhancedJavaScriptReviewer struct {
	treeSitterAnalyzer *JavaScriptTreeSitterAnalyzer
}

// NewEnhancedJavaScriptReviewer 创建增强的JavaScript审查器
func NewEnhancedJavaScriptReviewer() *EnhancedJavaScriptReviewer {
	return &EnhancedJavaScriptReviewer{
		treeSitterAnalyzer: NewJavaScriptTreeSitterAnalyzer(),
	}
}

// ReviewJavaScriptCode 审查JavaScript代码，结合Tree-sitter分析和现代JS最佳实践检查
func (reviewer *EnhancedJavaScriptReviewer) ReviewJavaScriptCode(code string, fileName string) (*JavaScriptCodeReviewResult, error) {
	logger.Infof("开始使用Tree-sitter对JavaScript代码进行增强审查: %s", fileName)

	// 使用Tree-sitter进行深度分析
	analysisResult, err := reviewer.treeSitterAnalyzer.AnalyzeJavaScriptCode(code)
	if err != nil {
		logger.Warnf("Tree-sitter分析失败，回退到传统方法: %v", err)
		return reviewer.fallbackReview(code, fileName)
	}

	// 构建审查结果
	result := &JavaScriptCodeReviewResult{
		FileName:          fileName,
		TreeSitterResult:  analysisResult,
		CodeSmells:        []string{},
		PerformanceIssues: []string{},
		SecurityIssues:    []string{},
		BestPractices:     []string{},
		QualityScore:      85, // 基础分数
	}

	// 基于Tree-sitter分析结果进行具体审查
	reviewer.analyzeCodeSmells(result)
	reviewer.analyzePerformanceIssues(result)
	reviewer.analyzeSecurityIssues(result)
	reviewer.analyzeBestPractices(result)

	// 计算质量分数
	reviewer.calculateQualityScore(result)

	logger.Infof("JavaScript代码审查完成: %s", fileName)

	return result, nil
}

// analyzeCodeSmells 分析JavaScript代码异味
func (reviewer *EnhancedJavaScriptReviewer) analyzeCodeSmells(result *JavaScriptCodeReviewResult) {
	analysis := result.TreeSitterResult

	// 检查函数复杂度
	for _, function := range analysis.Functions {
		if len(function.Body) > 1000 { // 函数体过长
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("长函数: %s (第%d行) - 函数体过长，建议拆分",
					function.Name, function.Position.StartRow+1))
		}

		if len(function.Parameters) > 5 { // 参数过多
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("参数过多: %s (第%d行) - 有%d个参数，建议重构",
					function.Name, function.Position.StartRow+1, len(function.Parameters)))
		}
	}

	// 检查变量命名
	for _, call := range analysis.FunctionCalls {
		if reviewer.isNonDescriptiveName(call.Method) {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("命名不清晰: '%s' (第%d行) - 建议使用更描述性的名称",
					call.Method, call.LineNumber))
		}
	}

	// 检查导入过多
	if len(analysis.Imports) > 20 {
		result.CodeSmells = append(result.CodeSmells,
			fmt.Sprintf("导入过多: 发现%d个导入，建议重构模块结构", len(analysis.Imports)))
	}
}

// analyzePerformanceIssues 分析JavaScript性能问题
func (reviewer *EnhancedJavaScriptReviewer) analyzePerformanceIssues(result *JavaScriptCodeReviewResult) {
	analysis := result.TreeSitterResult

	// 事件监听器泄漏
	for _, leak := range analysis.EventListenerLeaks {
		result.PerformanceIssues = append(result.PerformanceIssues, leak)
	}

	// 无限循环风险
	for _, loop := range analysis.InfiniteLoops {
		result.PerformanceIssues = append(result.PerformanceIssues, loop)
	}

	// 异步/等待问题
	for _, asyncIssue := range analysis.AsyncAwaitIssues {
		result.PerformanceIssues = append(result.PerformanceIssues, asyncIssue)
	}

	// 检查DOM操作频率
	domOperations := 0
	for _, call := range analysis.FunctionCalls {
		if reviewer.isDOMOperation(call.Method) {
			domOperations++
		}
	}
	if domOperations > 10 {
		result.PerformanceIssues = append(result.PerformanceIssues,
			fmt.Sprintf("DOM操作过频: 发现%d个DOM操作，建议批量处理或使用虚拟DOM", domOperations))
	}
}

// analyzeSecurityIssues 分析JavaScript安全问题
func (reviewer *EnhancedJavaScriptReviewer) analyzeSecurityIssues(result *JavaScriptCodeReviewResult) {
	analysis := result.TreeSitterResult

	// XSS风险
	for _, xss := range analysis.XSSRisks {
		result.SecurityIssues = append(result.SecurityIssues, xss)
	}

	// 检查eval使用
	for _, call := range analysis.FunctionCalls {
		if call.Method == "eval" {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("eval使用: 第%d行 - eval()存在安全风险，建议使用替代方案", call.LineNumber))
		}
	}

	// 检查敏感信息泄露
	for _, imp := range analysis.Imports {
		if reviewer.isSensitivePackage(imp.Package) {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("敏感包导入: '%s' (第%d行) - 可能包含敏感功能",
					imp.Package, imp.Position.StartRow+1))
		}
	}
}

// analyzeBestPractices 分析JavaScript最佳实践
func (reviewer *EnhancedJavaScriptReviewer) analyzeBestPractices(result *JavaScriptCodeReviewResult) {
	analysis := result.TreeSitterResult

	// React Hook问题
	for _, hookIssue := range analysis.ReactHookIssues {
		result.BestPractices = append(result.BestPractices, hookIssue)
	}

	// 检查ES6+特性使用
	if reviewer.lacksModernJS(analysis) {
		result.BestPractices = append(result.BestPractices,
			"建议使用现代JavaScript特性: 如箭头函数、解构、模板字符串等")
	}

	// 检查错误处理
	hasErrorHandling := false
	for _, call := range analysis.FunctionCalls {
		if reviewer.isErrorHandling(call.Method) {
			hasErrorHandling = true
			break
		}
	}
	if !hasErrorHandling {
		result.BestPractices = append(result.BestPractices,
			"缺少错误处理: 建议添加try-catch或Promise.catch处理")
	}

	// 检查类型检查
	if len(analysis.Functions) > 5 && !reviewer.hasTypeChecking(result.FileName) {
		result.BestPractices = append(result.BestPractices,
			"建议添加类型检查: 考虑使用TypeScript或JSDoc注释")
	}
}

// calculateQualityScore 计算JavaScript代码质量分数
func (reviewer *EnhancedJavaScriptReviewer) calculateQualityScore(result *JavaScriptCodeReviewResult) {
	score := 100

	// 扣分规则
	score -= len(result.CodeSmells) * 5        // 每个代码异味扣5分
	score -= len(result.PerformanceIssues) * 8 // 每个性能问题扣8分
	score -= len(result.SecurityIssues) * 12   // 每个安全问题扣12分
	score -= len(result.BestPractices) * 3     // 每个最佳实践建议扣3分

	// 确保分数在合理范围内
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	result.QualityScore = score
}

// fallbackReview 回退审查方法
func (reviewer *EnhancedJavaScriptReviewer) fallbackReview(code string, fileName string) (*JavaScriptCodeReviewResult, error) {
	logger.Warnf("使用回退方法审查JavaScript代码: %s", fileName)

	result := &JavaScriptCodeReviewResult{
		FileName:          fileName,
		CodeSmells:        []string{},
		PerformanceIssues: []string{},
		SecurityIssues:    []string{},
		BestPractices:     []string{},
		QualityScore:      70, // 回退方法给较低基础分
	}

	// 简单的基于正则的检查
	lines := strings.Split(code, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		lineNum := i + 1

		// 检查eval使用
		if strings.Contains(line, "eval(") {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("eval使用: 第%d行 - 存在安全风险", lineNum))
		}

		// 检查innerHTML使用
		if strings.Contains(line, "innerHTML") {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("innerHTML使用: 第%d行 - 可能的XSS风险", lineNum))
		}

		// 检查addEventListener
		if strings.Contains(line, "addEventListener") {
			result.PerformanceIssues = append(result.PerformanceIssues,
				fmt.Sprintf("事件监听器: 第%d行 - 注意移除监听器", lineNum))
		}
	}

	reviewer.calculateQualityScore(result)

	return result, nil
}

// 辅助判断方法
func (reviewer *EnhancedJavaScriptReviewer) isNonDescriptiveName(name string) bool {
	nonDescriptive := []string{"a", "b", "c", "data", "temp", "val", "obj", "x", "y", "z"}
	for _, nd := range nonDescriptive {
		if name == nd {
			return true
		}
	}
	return false
}

func (reviewer *EnhancedJavaScriptReviewer) isDOMOperation(method string) bool {
	domMethods := []string{
		"getElementById", "getElementsByClassName", "querySelector", "querySelectorAll",
		"createElement", "appendChild", "removeChild", "innerHTML", "textContent",
	}
	for _, dm := range domMethods {
		if method == dm {
			return true
		}
	}
	return false
}

func (reviewer *EnhancedJavaScriptReviewer) isSensitivePackage(pkg string) bool {
	sensitivePackages := []string{
		"crypto", "fs", "path", "child_process", "vm",
	}
	for _, sp := range sensitivePackages {
		if strings.Contains(pkg, sp) {
			return true
		}
	}
	return false
}

func (reviewer *EnhancedJavaScriptReviewer) lacksModernJS(analysis *JavaScriptAnalysisResult) bool {
	// 检查是否使用现代JavaScript特性
	// 这里简化处理，实际应该检查具体的语法树特征
	return len(analysis.Functions) > 3 // 简化的判断逻辑
}

func (reviewer *EnhancedJavaScriptReviewer) isErrorHandling(method string) bool {
	errorMethods := []string{"catch", "finally", "try"}
	for _, em := range errorMethods {
		if strings.Contains(method, em) {
			return true
		}
	}
	return false
}

func (reviewer *EnhancedJavaScriptReviewer) hasTypeChecking(fileName string) bool {
	// 检查是否为TypeScript文件或有类型注释
	return strings.HasSuffix(fileName, ".ts") || strings.HasSuffix(fileName, ".tsx")
}

// JavaScriptCodeReviewResult JavaScript代码审查结果
type JavaScriptCodeReviewResult struct {
	FileName          string                    `json:"file_name"`
	TreeSitterResult  *JavaScriptAnalysisResult `json:"tree_sitter_result,omitempty"`
	CodeSmells        []string                  `json:"code_smells"`
	PerformanceIssues []string                  `json:"performance_issues"`
	SecurityIssues    []string                  `json:"security_issues"`
	BestPractices     []string                  `json:"best_practices"`
	QualityScore      int                       `json:"quality_score"`
}
