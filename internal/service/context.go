package service

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"path/filepath"
	"regexp"
	"strings"

	"ai-codereview-service/internal/gitlab"
	"ai-codereview-service/pkg/logger"
)

// ContextInfo 上下文信息
type ContextInfo struct {
	FileContent  string   `json:"file_content"`
	Imports      []string `json:"imports"`
	Functions    []string `json:"functions"`
	Classes      []string `json:"classes"`
	Dependencies []string `json:"dependencies"`
	RelatedFiles []string `json:"related_files"`
}

// ContextExtractor 上下文信息提取器
type ContextExtractor struct {
	gitlabClient    *gitlab.Client
	detectedModules []string // 动态检测到的模块列表
}

// NewContextExtractor 创建上下文提取器
func NewContextExtractor(gitlabClient *gitlab.Client) *ContextExtractor {
	return &ContextExtractor{
		gitlabClient:    gitlabClient,
		detectedModules: []string{}, // 初始为空，将动态检测
	}
}

// detectProjectModules 检测项目的实际模块结构
func (ce *ContextExtractor) detectProjectModules(branchName string) error {
	logger.Infof("动态检测项目模块结构...")

	// 获取项目根目录结构
	tree, err := ce.gitlabClient.ListRepositoryTree(branchName, "")
	if err != nil {
		logger.Warnf("无法获取项目根目录结构: %v", err)
		return err
	}

	// 查找实际存在的模块目录
	var detectedModules []string
	for _, item := range tree {
		if path, ok := item["path"].(string); ok {
			if itemType, ok := item["type"].(string); ok && itemType == "tree" {
				// 检查是否是Maven模块（包含pom.xml）
				if ce.isValidModule(path, branchName) {
					detectedModules = append(detectedModules, path)
					logger.Infof("检测到有效模块: %s", path)
				}
			}
		}
	}

	ce.detectedModules = detectedModules
	logger.Infof("检测完成，共发现 %d 个模块: %v", len(detectedModules), detectedModules)

	return nil
}

// isValidModule 检查目录是否为有效的Maven模块
func (ce *ContextExtractor) isValidModule(modulePath, branchName string) bool {
	// 检查模块目录下是否有pom.xml
	pomPath := fmt.Sprintf("%s/pom.xml", modulePath)
	_, err := ce.gitlabClient.GetFileContentFromBranch(pomPath, branchName)
	return err == nil
}

// ExtractFromGoFile 从Go文件中提取上下文信息
func (ce *ContextExtractor) ExtractFromGoFile(fileContent string) *ContextInfo {
	context := &ContextInfo{
		FileContent:  fileContent,
		Imports:      []string{},
		Functions:    []string{},
		Classes:      []string{},
		Dependencies: []string{},
		RelatedFiles: []string{},
	}

	// 解析Go代码
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, "", fileContent, parser.ParseComments)
	if err != nil {
		logger.Warnf("Failed to parse Go file: %v", err)
		return context
	}

	// 提取imports
	for _, imp := range node.Imports {
		importPath := strings.Trim(imp.Path.Value, `"`)
		context.Imports = append(context.Imports, importPath)
	}

	// 提取函数和方法
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.FuncDecl:
			if x.Name != nil {
				funcName := x.Name.Name
				if x.Recv != nil {
					// 方法
					context.Functions = append(context.Functions, fmt.Sprintf("(%s) %s", getReceiverType(x.Recv), funcName))
				} else {
					// 函数
					context.Functions = append(context.Functions, funcName)
				}
			}
		case *ast.TypeSpec:
			// 提取类型定义（结构体、接口等）
			if x.Name != nil {
				context.Classes = append(context.Classes, x.Name.Name)
			}
		}
		return true
	})

	return context
}

// ExtractFromOtherFile 从其他类型文件中提取上下文信息
func (ce *ContextExtractor) ExtractFromOtherFile(fileContent, filePath string) *ContextInfo {
	context := &ContextInfo{
		FileContent:  fileContent,
		Imports:      []string{},
		Functions:    []string{},
		Classes:      []string{},
		Dependencies: []string{},
		RelatedFiles: []string{},
	}

	ext := strings.ToLower(filepath.Ext(filePath))

	switch ext {
	case ".js", ".ts":
		context.Imports = ce.extractJSImports(fileContent)
		context.Functions = ce.extractJSFunctions(fileContent)
		context.Classes = ce.extractJSClasses(fileContent)
	case ".java":
		context.Imports = ce.extractJavaImports(fileContent)
		context.Functions = ce.extractJavaMethods(fileContent)
		context.Classes = ce.extractJavaClasses(fileContent)
	case ".py":
		context.Imports = ce.extractPythonImports(fileContent)
		context.Functions = ce.extractPythonFunctions(fileContent)
		context.Classes = ce.extractPythonClasses(fileContent)
	}

	return context
}

// GetFileContentFromGitLab 从GitLab获取文件内容
func (ce *ContextExtractor) GetFileContentFromGitLab(filePath, ref string) (string, error) {
	return ce.GetFileContentFromGitLabSimple(filePath, ref)
}

// GetFileContentFromGitLabWithFallback 从GitLab获取文件内容，支持指定回退分支
func (ce *ContextExtractor) GetFileContentFromGitLabWithFallback(filePath, ref string, fallbackBranch string) (string, error) {
	return ce.GetFileContentFromGitLabSimple(filePath, fallbackBranch)
}

// GetFileContentFromGitLabSimple 简化的文件获取逻辑 - 从指定ref（分支或commit）获取
func (ce *ContextExtractor) GetFileContentFromGitLabSimple(filePath, ref string) (string, error) {
	if ref == "" {
		// 不再硬编码test分支，而是使用项目默认分支
		defaultBranch, err := ce.gitlabClient.GetDefaultBranch()
		if err != nil {
			logger.Warnf("无法获取项目默认分支，使用master: %v", err)
			ref = "master"
		} else {
			ref = defaultBranch
		}
	}

	logger.Infof("从GitLab获取文件: %s (ref: %s)", filePath, ref)

	// 检查是否包含通配符，如果包含则先展开路径
	if strings.Contains(filePath, "*") {
		// 如果还没有检测过项目模块，先检测一次
		if len(ce.detectedModules) == 0 {
			ce.detectProjectModules(ref)
		}
		return ce.getFileContentWithWildcard(ref, filePath)
	}

	// 优先尝试从ref（可能是commit或分支）获取文件内容
	content, err := ce.gitlabClient.GetFileContentFromBranch(filePath, ref)
	if err != nil {
		logger.Warnf("从ref获取文件失败: %s (ref: %s), 错误: %v", filePath, ref, err)
		return "", fmt.Errorf("无法从ref %s 获取文件 %s: %w", ref, filePath, err)
	}

	logger.Infof("成功从ref获取文件: %s (长度: %d)", filePath, len(content))
	return content, nil
}

// getFileContentWithWildcard 处理包含通配符的文件路径
func (ce *ContextExtractor) getFileContentWithWildcard(branchName, filePath string) (string, error) {
	// 检查是否包含通配符
	if !strings.Contains(filePath, "*") {
		// 普通路径，直接获取内容
		return ce.gitlabClient.GetFileContent(filePath, branchName)
	}

	// 动态检测项目结构
	projectStructure, err := ce.detectProjectStructure(branchName)
	if err != nil {
		logger.Warnf("无法检测项目结构: %v", err)
		return "", err
	}

	// 展开通配符路径
	expandedPaths := ce.expandWildcardPath(filePath, projectStructure)

	// 尝试每个展开的路径
	for _, path := range expandedPaths {
		if content, err := ce.gitlabClient.GetFileContent(path, branchName); err == nil {
			logger.Debugf("成功通过展开路径获取文件: %s -> %s", filePath, path)
			return content, nil
		}
	}

	return "", fmt.Errorf("未找到匹配通配符 %s 的文件", filePath)
}

// detectProjectStructure 动态检测项目结构
func (ce *ContextExtractor) detectProjectStructure(branchName string) (*ProjectStructure, error) {
	logger.Debugf("开始动态检测项目结构...")

	// 检查根目录是否有pom.xml（Maven项目标识）
	if _, err := ce.gitlabClient.GetFileContent("pom.xml", branchName); err != nil {
		// 非Maven项目，返回简单结构
		return &ProjectStructure{
			IsMultiModule: false,
			Modules:       []string{},
			RootPath:      "",
		}, nil
	}

	// 临时检测模块结构（重用现有的detectProjectModules）
	if err := ce.detectProjectModules(branchName); err != nil {
		logger.Warnf("模块检测失败: %v", err)
	}

	projectStructure := &ProjectStructure{
		IsMultiModule: len(ce.detectedModules) > 0,
		Modules:       ce.detectedModules,
		RootPath:      "",
	}

	logger.Infof("检测到项目结构: 多模块=%v, 模块数量=%d, 模块列表=%v",
		projectStructure.IsMultiModule, len(ce.detectedModules), ce.detectedModules)

	return projectStructure, nil
}

// expandWildcardPath 展开包含通配符的路径
func (ce *ContextExtractor) expandWildcardPath(wildcardPath string, projectStructure *ProjectStructure) []string {
	var expandedPaths []string

	// 移除通配符并生成实际路径
	if strings.HasPrefix(wildcardPath, "*/") {
		// 单层通配符 */path
		basePath := strings.TrimPrefix(wildcardPath, "*/")

		if projectStructure.IsMultiModule {
			// 为每个模块生成路径
			for _, module := range projectStructure.Modules {
				fullPath := fmt.Sprintf("%s/%s", module, basePath)
				expandedPaths = append(expandedPaths, fullPath)
			}
		} else {
			// 单模块项目，直接使用基础路径
			expandedPaths = append(expandedPaths, basePath)
		}
	} else if strings.HasPrefix(wildcardPath, "**/") {
		// 多层通配符 **/path (目前简化处理，类似单层)
		basePath := strings.TrimPrefix(wildcardPath, "**/")

		if projectStructure.IsMultiModule {
			for _, module := range projectStructure.Modules {
				fullPath := fmt.Sprintf("%s/%s", module, basePath)
				expandedPaths = append(expandedPaths, fullPath)
			}
		} else {
			expandedPaths = append(expandedPaths, basePath)
		}
	} else {
		// 没有通配符，直接返回
		expandedPaths = append(expandedPaths, wildcardPath)
	}

	logger.Debugf("通配符路径展开: %s -> %v", wildcardPath, expandedPaths)
	return expandedPaths
}

// 辅助函数：获取接收者类型
func getReceiverType(recv *ast.FieldList) string {
	if recv == nil || len(recv.List) == 0 {
		return ""
	}

	field := recv.List[0]
	switch t := field.Type.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.StarExpr:
		if ident, ok := t.X.(*ast.Ident); ok {
			return "*" + ident.Name
		}
	}
	return ""
}

// JavaScript/TypeScript 提取函数
func (ce *ContextExtractor) extractJSImports(content string) []string {
	var imports []string
	patterns := []string{
		`import\s+.*?\s+from\s+['"]([^'"]+)['"]`,
		`import\s+['"]([^'"]+)['"]`,
		`require\(['"]([^'"]+)['"]\)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				imports = append(imports, match[1])
			}
		}
	}

	return imports
}

func (ce *ContextExtractor) extractJSFunctions(content string) []string {
	var functions []string
	patterns := []string{
		`function\s+(\w+)\s*\(`,
		`(\w+)\s*:\s*function\s*\(`,
		`const\s+(\w+)\s*=\s*\(`,
		`let\s+(\w+)\s*=\s*\(`,
		`var\s+(\w+)\s*=\s*\(`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				functions = append(functions, match[1])
			}
		}
	}

	return functions
}

func (ce *ContextExtractor) extractJSClasses(content string) []string {
	var classes []string
	re := regexp.MustCompile(`class\s+(\w+)`)
	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			classes = append(classes, match[1])
		}
	}
	return classes
}

// Java 提取函数
func (ce *ContextExtractor) extractJavaImports(content string) []string {
	var imports []string
	re := regexp.MustCompile(`import\s+([a-zA-Z_][a-zA-Z0-9_.*]*);`)
	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			imports = append(imports, match[1])
		}
	}
	return imports
}

func (ce *ContextExtractor) extractJavaMethods(content string) []string {
	var methods []string
	re := regexp.MustCompile(`(?:public|private|protected|static|\s)+[\w<>\[\]]+\s+(\w+)\s*\([^)]*\)\s*\{`)
	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			methods = append(methods, match[1])
		}
	}
	return methods
}

func (ce *ContextExtractor) extractJavaClasses(content string) []string {
	var classes []string
	re := regexp.MustCompile(`(?:public|private|protected|\s)*class\s+(\w+)`)
	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			classes = append(classes, match[1])
		}
	}
	return classes
}

// Python 提取函数
func (ce *ContextExtractor) extractPythonImports(content string) []string {
	var imports []string
	patterns := []string{
		`import\s+([a-zA-Z_][a-zA-Z0-9_.]*)`,
		`from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				imports = append(imports, match[1])
			}
		}
	}

	return imports
}

func (ce *ContextExtractor) extractPythonFunctions(content string) []string {
	var functions []string
	re := regexp.MustCompile(`def\s+(\w+)\s*\(`)
	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			functions = append(functions, match[1])
		}
	}
	return functions
}

func (ce *ContextExtractor) extractPythonClasses(content string) []string {
	var classes []string
	re := regexp.MustCompile(`class\s+(\w+)`)
	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			classes = append(classes, match[1])
		}
	}
	return classes
}
