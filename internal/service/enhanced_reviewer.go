package service

import (
	"fmt"
	"path/filepath"
	"sort"
	"strings"

	"ai-codereview-service/internal/gitlab"
	"ai-codereview-service/internal/llm"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

// EnhancedCodeReviewer 增强版代码审查器
type EnhancedCodeReviewer struct {
	contextExtractor *ContextExtractor
	llmClient        *llm.Client
	maxTokens        int
	smartTruncator   *SmartTruncator
}

// NewEnhancedCodeReviewer 创建增强代码审查器
func NewEnhancedCodeReviewer(gitlabClient *gitlab.Client, llmClient *llm.Client, maxTokens int) *EnhancedCodeReviewer {
	// 优先保证完整上下文，充分利用80k上下文
	// 单文件token限制，优先保证完整上下文
	singleFileMaxTokens := maxTokens * 9 / 10 // 90%分配给单文件

	// 设置合理的最小值，确保能够收集完整的上下文
	if singleFileMaxTokens < 40000 {
		singleFileMaxTokens = 40000 // 最低保证40k tokens
	}

	logger.Infof("增强审查器配置: 单文件最大tokens=%d (总配置:%d), 优先全量上下文", singleFileMaxTokens, maxTokens)

	return &EnhancedCodeReviewer{
		contextExtractor: NewContextExtractor(gitlabClient),
		llmClient:        llmClient,
		maxTokens:        maxTokens,
		smartTruncator:   NewSmartTruncator(singleFileMaxTokens),
	}
}

// GatherContextForChanges 收集变更文件的上下文信息
func (ecr *EnhancedCodeReviewer) GatherContextForChanges(changes []repository.GitLabChange, ref string) map[string]*ContextInfo {
	return ecr.GatherContextForChangesWithBranch(changes, ref, "")
}

// GatherContextForChangesWithBranch 收集变更文件的上下文信息，支持指定回退分支
func (ecr *EnhancedCodeReviewer) GatherContextForChangesWithBranch(changes []repository.GitLabChange, ref string, fallbackBranch string) map[string]*ContextInfo {
	contextMap := make(map[string]*ContextInfo)
	totalFiles := len(changes)
	successCount := 0
	failureCount := 0

	// 确定实际使用的分支
	targetBranch := fallbackBranch
	if targetBranch == "" {
		targetBranch = ref
	}

	logger.Infof("开始收集上下文信息：总文件数 %d，目标分支: %s", totalFiles, targetBranch)

	// 调试：输出所有变更的详细信息
	for i, change := range changes {
		logger.Infof("变更 #%d: NewPath=%s, OldPath=%s, NewFile=%v, DeletedFile=%v, RenamedFile=%v",
			i+1, change.NewPath, change.OldPath, change.NewFile, change.DeletedFile, change.RenamedFile)
	}

	for _, change := range changes {
		var filePath string

		// 简化文件路径选择逻辑
		if change.DeletedFile {
			// 跳过已删除文件
			logger.Infof("跳过已删除文件: %s", change.OldPath)
			continue
		} else if change.NewPath != "" {
			// 使用新路径（涵盖新增、修改、重命名）
			filePath = change.NewPath
		} else if change.OldPath != "" {
			// 备选使用旧路径
			filePath = change.OldPath
		} else {
			logger.Warnf("文件路径为空，跳过此文件变更")
			continue
		}

		logger.Infof("收集文件上下文: %s (分支: %s, 类型: %s)", filePath, targetBranch, ecr.getChangeType(change))

		// 尝试获取文件内容，支持路径智能修正
		fileContent, err := ecr.getFileContentWithPathCorrection(filePath, targetBranch)
		if err != nil {
			logger.Warnf("无法获取文件内容: %s, 错误: %v", filePath, err)
			failureCount++
			continue
		}

		// 使用智能截断保留重要上下文
		truncatedContent := ecr.smartTruncator.TruncateWithContext(fileContent, change, filePath)

		// 根据文件类型提取上下文
		var context *ContextInfo
		ext := strings.ToLower(filepath.Ext(filePath))
		if ext == ".go" {
			context = ecr.contextExtractor.ExtractFromGoFile(fileContent)
		} else {
			context = ecr.contextExtractor.ExtractFromOtherFile(fileContent, filePath)
		}

		// 使用截断后的内容作为文件内容
		context.FileContent = truncatedContent
		contextMap[filePath] = context
		successCount++
	}

	logger.Infof("上下文收集完成：成功 %d 个，失败 %d 个，总计 %d 个文件", successCount, failureCount, totalFiles)

	// 如果所有文件都失败了，调用增强诊断功能
	if successCount == 0 && totalFiles > 0 {
		ecr.diagnoseFailedFiles(changes, targetBranch)
	}

	return contextMap
}

// GatherFullContextWithDependencies 收集全量上下文，包含依赖文件（充分利用80k容量）
func (ecr *EnhancedCodeReviewer) GatherFullContextWithDependencies(changes []repository.GitLabChange, ref, branchName string, dependencies map[string][]string) map[string]*ContextInfo {
	contextMap := make(map[string]*ContextInfo)
	allFiles := make(map[string]bool) // 去重用

	// 1. 收集变更文件
	for _, change := range changes {
		var filePath string
		if change.DeletedFile {
			continue
		} else if change.NewPath != "" {
			filePath = change.NewPath
		} else if change.OldPath != "" {
			filePath = change.OldPath
		}

		if filePath != "" {
			allFiles[filePath] = true
		}
	}

	// 2. 收集依赖文件（多级递归）
	for _, depList := range dependencies {
		for _, depFile := range depList {
			allFiles[depFile] = true
		}
	}

	logger.Infof("全量上下文收集: 总文件数=%d (变更文件:%d + 依赖文件:%d)",
		len(allFiles), len(changes), len(allFiles)-len(changes))

	// 3. 获取所有文件的完整内容（不进行激进截断）
	successCount := 0
	for filePath := range allFiles {
		fileContent, err := ecr.contextExtractor.GetFileContentFromGitLabSimple(filePath, branchName)
		if err != nil {
			logger.Debugf("无法获取文件内容: %s, 错误: %v", filePath, err)
			continue
		}

		// 只在内容极大时才进行温和截断，优先保证完整性
		var processedContent string
		if llm.CountTokens(fileContent) > ecr.smartTruncator.maxTokens {
			// 寻找对应的change信息来进行智能截断
			var relatedChange *repository.GitLabChange
			for _, change := range changes {
				if change.NewPath == filePath || change.OldPath == filePath {
					relatedChange = &change
					break
				}
			}

			if relatedChange != nil {
				processedContent = ecr.smartTruncator.TruncateWithContext(fileContent, *relatedChange, filePath)
			} else {
				// 对于依赖文件，使用更保守的截断策略
				processedContent = ecr.conservativeTruncate(fileContent, filePath)
			}
		} else {
			processedContent = fileContent // 保留完整内容
		}

		// 提取上下文信息
		var context *ContextInfo
		ext := strings.ToLower(filepath.Ext(filePath))
		if ext == ".go" {
			context = ecr.contextExtractor.ExtractFromGoFile(fileContent)
		} else {
			context = ecr.contextExtractor.ExtractFromOtherFile(fileContent, filePath)
		}

		context.FileContent = processedContent
		contextMap[filePath] = context
		successCount++
	}

	logger.Infof("全量上下文收集完成: 成功=%d, 失败=%d", successCount, len(allFiles)-successCount)
	return contextMap
}

// conservativeTruncate 保守的截断策略，用于依赖文件
func (ecr *EnhancedCodeReviewer) conservativeTruncate(content, filePath string) string {
	lines := strings.Split(content, "\n")
	if len(lines) <= 100 {
		return content // 小文件不截断
	}

	// 保留文件头部（包含重要的声明和导入）和底部的重要部分
	headerLines := 50
	footerLines := 30

	if len(lines) <= headerLines+footerLines {
		return content
	}

	var result strings.Builder

	// 保留头部
	for i := 0; i < headerLines && i < len(lines); i++ {
		result.WriteString(lines[i])
		result.WriteString("\n")
	}

	result.WriteString(fmt.Sprintf("\n// ... [截断 %d 行] ...\n\n", len(lines)-headerLines-footerLines))

	// 保留尾部
	startIdx := len(lines) - footerLines
	for i := startIdx; i < len(lines); i++ {
		result.WriteString(lines[i])
		result.WriteString("\n")
	}

	return result.String()
}

// ReviewWithFullContext 使用全量上下文进行审查
func (ecr *EnhancedCodeReviewer) ReviewWithFullContext(changesText, commitsText string, contextMap map[string]*ContextInfo, languageReview, primaryLanguage string) (string, error) {
	logger.Infof("开始全量上下文审查: 上下文文件数=%d, 语言=%s", len(contextMap), primaryLanguage)

	// 构建增强的提示词
	prompt := ecr.BuildFullContextPrompt(changesText, commitsText, contextMap, languageReview, primaryLanguage)

	messages := []llm.Message{
		{Role: "system", Content: ecr.buildAdvancedSystemPrompt(primaryLanguage)},
		{Role: "user", Content: prompt},
	}

	// 调用LLM进行审查
	response, err := ecr.llmClient.ChatCompletion(messages)
	if err != nil {
		return "", fmt.Errorf("LLM审查失败: %w", err)
	}

	return response, nil
}

// BuildFullContextPrompt 构建全量上下文提示词
func (ecr *EnhancedCodeReviewer) BuildFullContextPrompt(changesText, commitsText string, contextMap map[string]*ContextInfo, languageReview, primaryLanguage string) string {
	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("## 📋 代码审查任务 (%s项目)\n\n", primaryLanguage))

	// 1. 提交信息
	if commitsText != "" {
		builder.WriteString("### 📝 提交信息:\n")
		builder.WriteString(commitsText)
		builder.WriteString("\n\n")
	}

	// 2. 语言特定审查结果
	if languageReview != "" {
		builder.WriteString("### 🔍 语言特定审查结果:\n")
		builder.WriteString(languageReview)
		builder.WriteString("\n\n")
	}

	// 3. 完整上下文信息
	builder.WriteString("### 🌍 完整代码上下文:\n")
	builder.WriteString(fmt.Sprintf("共收集到 %d 个相关文件的上下文信息，包括变更文件和依赖文件：\n\n", len(contextMap)))

	// 按重要性排序文件
	sortedFiles := ecr.sortFilesByImportanceForFullContext(contextMap, changesText)

	for i, filePath := range sortedFiles {
		if i >= 20 { // 限制最多显示20个文件的详细内容
			builder.WriteString(fmt.Sprintf("... 其他 %d 个文件省略显示\n", len(sortedFiles)-i))
			break
		}

		context := contextMap[filePath]
		builder.WriteString(fmt.Sprintf("#### 📄 文件: %s\n", filePath))

		// 显示文件的元信息
		if len(context.Imports) > 0 {
			builder.WriteString(fmt.Sprintf("**导入**: %s\n", strings.Join(context.Imports[:minInt(5, len(context.Imports))], ", ")))
		}
		if len(context.Functions) > 0 {
			builder.WriteString(fmt.Sprintf("**函数**: %s\n", strings.Join(context.Functions[:minInt(3, len(context.Functions))], ", ")))
		}
		if len(context.Classes) > 0 {
			builder.WriteString(fmt.Sprintf("**类/接口**: %s\n", strings.Join(context.Classes[:minInt(3, len(context.Classes))], ", ")))
		}

		// 显示文件内容（完整或截断后的）
		builder.WriteString("```")
		builder.WriteString(getFileLanguage(filePath))
		builder.WriteString("\n")
		builder.WriteString(context.FileContent)
		builder.WriteString("\n```\n\n")
	}

	// 4. 代码变更详情
	builder.WriteString("### 🔄 代码变更详情:\n")
	builder.WriteString(changesText)
	builder.WriteString("\n\n")

	// 5. 审查要求
	builder.WriteString("### 📋 审查要求:\n")
	builder.WriteString("请基于以上完整的上下文信息，深入分析代码变更的:")
	builder.WriteString("\n1. **业务逻辑正确性** - 是否存在逻辑缺陷或边界情况处理不当")
	builder.WriteString("\n2. **跨文件影响分析** - 变更对依赖文件和被依赖文件的影响")
	builder.WriteString("\n3. **架构一致性** - 是否符合项目的分层架构和设计模式")
	builder.WriteString("\n4. **性能影响** - 是否可能导致性能问题或资源浪费")
	builder.WriteString("\n5. **安全风险** - 是否存在安全漏洞或潜在风险")

	if primaryLanguage == "java" {
		builder.WriteString("\n6. **Dubbo框架规范** - 是否符合Dubbo 2.7和公司Java开发规范")
	}

	builder.WriteString("\n\n请提供详细的分析和具体的改进建议。")

	return builder.String()
}

// buildAdvancedSystemPrompt 构建高级系统提示词
func (ecr *EnhancedCodeReviewer) buildAdvancedSystemPrompt(primaryLanguage string) string {
	basePrompt := `你是一位资深的软件架构师和代码审查专家，拥有多年的企业级项目开发经验。

### 审查能力:
- 深度业务逻辑分析，能发现潜在的逻辑缺陷和边界情况
- 跨文件依赖关系分析，理解代码变更的全局影响
- 架构一致性检查，确保代码符合项目的设计原则
- 性能和安全风险评估

### 审查原则:
1. **上下文感知**: 基于完整的文件上下文进行分析，而非孤立地看待代码片段
2. **业务导向**: 重点关注业务逻辑的正确性和健壮性
3. **实用性**: 提供可操作的具体改进建议
4. **严谨性**: 识别潜在的bug、性能问题和安全风险`

	// 根据主要语言添加特定指导
	switch primaryLanguage {
	case "java":
		basePrompt += `

### Java/Dubbo项目专项要求:
- **分层架构**: Controller -> Manager -> Repository，各层职责清晰
- **依赖注入**: 正确使用@Resource、@DubboService等注解
- **异常处理**: 统一使用YppRunTimeException，避免返回true/false
- **性能优化**: 识别N+1查询、建议并行查询优化
- **上下文安全**: 确保通过MobileAPIContext获取用户信息
- **Dubbo规范**: 注解位置、服务实现、版本兼容性检查`
	case "go":
		basePrompt += `

### Go项目专项要求:
- **错误处理**: 检查error处理的完整性
- **并发安全**: goroutine和channel的正确使用
- **内存管理**: 防止内存泄漏和资源未释放
- **接口设计**: 符合Go的接口设计哲学`
	}

	basePrompt += `

### 输出要求:
请以清晰、结构化的方式输出审查结果，包含问题等级(错误❌/警告⚠️/建议💡)和具体改进方案。`

	return basePrompt
}

// sortFilesByImportanceForFullContext 按重要性对文件进行排序（全量上下文版本）
func (ecr *EnhancedCodeReviewer) sortFilesByImportanceForFullContext(contextMap map[string]*ContextInfo, changesText string) []string {
	files := make([]string, 0, len(contextMap))
	for filePath := range contextMap {
		files = append(files, filePath)
	}

	// 简单的重要性排序：变更文件 > 核心业务文件 > 依赖文件
	sort.Slice(files, func(i, j int) bool {
		scoreI := ecr.calculateFileImportanceScore(files[i], changesText)
		scoreJ := ecr.calculateFileImportanceScore(files[j], changesText)
		return scoreI > scoreJ
	})

	return files
}

// calculateFileImportanceScore 计算文件重要性分数
func (ecr *EnhancedCodeReviewer) calculateFileImportanceScore(filePath, changesText string) int {
	score := 0

	// 变更文件得分最高
	if strings.Contains(changesText, filePath) {
		score += 100
	}

	// 核心业务文件
	if strings.Contains(filePath, "Controller") {
		score += 50
	} else if strings.Contains(filePath, "Service") || strings.Contains(filePath, "Manager") {
		score += 40
	} else if strings.Contains(filePath, "Repository") || strings.Contains(filePath, "Mapper") {
		score += 30
	}

	// 配置文件
	if strings.Contains(filePath, "pom.xml") || strings.Contains(filePath, "application") {
		score += 20
	}

	// Java文件优先
	if strings.HasSuffix(filePath, ".java") {
		score += 10
	}

	return score
}

// getFileLanguage 获取文件语言标识
func getFileLanguage(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".java":
		return "java"
	case ".go":
		return "go"
	case ".py":
		return "python"
	case ".js":
		return "javascript"
	case ".ts":
		return "typescript"
	case ".xml":
		return "xml"
	case ".yml", ".yaml":
		return "yaml"
	case ".json":
		return "json"
	default:
		return ""
	}
}

// minInt 辅助函数，避免重复声明
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// getChangeType 获取变更类型的描述
func (ecr *EnhancedCodeReviewer) getChangeType(change repository.GitLabChange) string {
	if change.DeletedFile {
		return "已删除"
	} else if change.NewFile {
		return "新增"
	} else if change.RenamedFile {
		return "重命名"
	} else {
		return "修改"
	}
}

// BuildEnhancedPrompt 构建增强的审查提示词 - 优化动态分配
func (ecr *EnhancedCodeReviewer) BuildEnhancedPrompt(changesText, commitsText string, contextMap map[string]*ContextInfo) string {
	var builder strings.Builder

	// 基础提示词 - 80000 tokens 超大上下文版本
	builder.WriteString(`# 🔍 深度代码审查任务

## 🎯 审查目标
作为一名资深的代码审查专家，请对以下代码变更进行**全面深入**的审查。基于提供的丰富上下文信息，进行多维度分析。

### 🔥 核心审查要求

#### 1. 业务逻辑完整性分析
- **逻辑正确性**: 深入分析业务逻辑是否存在bug、遗漏的边界条件、异常处理不当等问题
- **数据流分析**: 跟踪数据在系统中的流转，确保数据的完整性和一致性
- **状态管理**: 检查对象状态的变更是否合理，状态机是否正确实现
- **业务规则验证**: 确保代码实现符合业务需求和约束条件

#### 2. 系统架构与设计分析
- **模块职责**: 评估代码变更是否符合单一职责原则和模块边界
- **依赖关系**: 分析新增的依赖是否合理，是否引入循环依赖
- **接口设计**: 检查API接口的设计是否符合RESTful规范和最佳实践
- **扩展性考虑**: 评估代码的可扩展性和可维护性

#### 3. 安全与性能深度分析
- **安全漏洞检测**: 深入检查SQL注入、XSS攻击、权限绕过等安全问题
- **性能影响评估**: 分析算法复杂度、数据库查询效率、内存使用等性能问题
- **并发安全**: 检查多线程环境下的竞态条件、死锁风险等并发问题
- **资源管理**: 确保资源的正确获取和释放，防止内存泄漏

#### 4. 代码质量与规范
- **代码风格**: 检查命名规范、代码结构、注释质量等
- **错误处理**: 验证异常处理的完整性和恰当性
- **测试覆盖**: 评估是否需要补充单元测试或集成测试
- **文档完整性**: 检查是否需要更新相关文档

## 📋 详细输出格式

请以结构化的Markdown格式输出**详细**的代码审查报告：

### 🚨 严重问题 (Critical Issues)
如发现以下问题，请详细说明：
- 可能导致系统故障、数据丢失、安全漏洞的问题
- 需要立即修复的业务逻辑错误
- 性能严重劣化的代码
- 违反安全最佳实践的代码

**格式**: 
- **问题描述**: [具体问题]
- **影响范围**: [可能影响的功能或模块]
- **修复建议**: [具体的修复方案]
- **优先级**: [高/中/低]

### ⚠️ 潜在风险 (Potential Risks)
如发现以下问题，请详细分析：
- 边界条件处理不当
- 异常情况未完全覆盖
- 性能瓶颈或资源浪费
- 可维护性问题

**格式**:
- **风险描述**: [具体风险]
- **触发条件**: [什么情况下会出现]
- **影响评估**: [可能的后果]
- **建议措施**: [预防或缓解方案]

### 💡 优化建议 (Optimization Suggestions)
如有改进空间，请提供：
- 代码结构改进建议
- 性能优化机会
- 最佳实践应用
- 重构建议

**格式**:
- **优化点**: [具体的优化机会]
- **预期收益**: [优化后的预期效果]
- **实现方案**: [具体的实现建议]
- **实施难度**: [简单/中等/复杂]

### 📊 整体评估 (Overall Assessment)
- **变更复杂度**: [简单/中等/复杂/高复杂]
- **风险等级**: [低/中/高/极高]
- **建议措施**: [是否建议合并/需要修改/需要重新设计]
- **关注重点**: [后续开发需要特别关注的方面]

**注意：请基于提供的丰富上下文信息，进行深入、全面的分析，给出具体、可操作的建议。**

---
`)

	// 计算动态分配比例
	totalFiles := len(contextMap)
	changesTokens := llm.CountTokens(changesText)
	commitsTokens := llm.CountTokens(commitsText)

	// 预留tokens：系统提示词(~1000) + 提交信息 + 变更内容 + 响应预留(~2000)
	reservedTokens := 1000 + commitsTokens + changesTokens + 2000
	availableForContext := ecr.maxTokens - reservedTokens

	if availableForContext <= 0 {
		// 如果预留后没有剩余，压缩变更内容
		availableForContext = ecr.maxTokens / 3
		changesText = llm.TruncateByTokens(changesText, ecr.maxTokens/2)
		logger.Warnf("Token不足，压缩变更内容到 %d tokens", llm.CountTokens(changesText))
	}

	// 动态分配策略
	contextPerFile := availableForContext / max(totalFiles, 1)

	// 根据文件重要性调整分配
	fileImportanceMap := ecr.calculateFileImportance(contextMap, changesText)

	logger.Infof("动态Token分配 - 总可用: %d, 单文件平均: %d", availableForContext, contextPerFile)

	// 添加上下文信息
	if len(contextMap) > 0 {
		builder.WriteString("## 文件上下文信息\n\n")

		// 按重要性排序处理文件
		sortedFiles := ecr.sortFilesByImportance(contextMap, fileImportanceMap)

		for _, filePath := range sortedFiles {
			context := contextMap[filePath]
			importance := fileImportanceMap[filePath]

			// 根据重要性分配token
			fileTokenBudget := int(float64(contextPerFile) * importance)
			if fileTokenBudget < 500 {
				fileTokenBudget = 500 // 最小保证
			}

			builder.WriteString(fmt.Sprintf("### 文件：%s (重要性: %.1f)\n\n", filePath, importance))

			// 动态显示导入、函数、类型信息
			contextTokensUsed := ecr.addDynamicContextInfo(&builder, context, fileTokenBudget/3)

			// 剩余token用于文件内容
			remainingTokens := fileTokenBudget - contextTokensUsed
			if context.FileContent != "" && remainingTokens > 100 {
				truncatedContent := llm.TruncateByTokens(context.FileContent, remainingTokens)
				contentTokens := llm.CountTokens(truncatedContent)
				builder.WriteString(fmt.Sprintf("**文件内容（%d tokens，重要部分）：**\n```\n%s\n```\n\n",
					contentTokens, truncatedContent))
			}
		}
	}

	// 添加业务逻辑分析提示
	builder.WriteString("## 业务逻辑分析重点\n\n")
	builder.WriteString(ecr.generateBusinessLogicHints(contextMap, changesText))

	// 添加提交信息
	if commitsText != "" {
		builder.WriteString(fmt.Sprintf("## 提交信息\n%s\n\n", commitsText))
	}

	// 添加变更内容
	builder.WriteString(fmt.Sprintf("## 代码变更内容\n\n```diff\n%s\n```\n\n", changesText))

	builder.WriteString("## 审查要求\n")
	builder.WriteString("请基于以上完整的上下文信息，进行深入的代码审查。特别关注：\n")
	builder.WriteString("1. 变更是否可能破坏现有业务逻辑\n")
	builder.WriteString("2. 新增逻辑是否处理了所有边界情况\n")
	builder.WriteString("3. 错误处理是否完整和恰当\n")
	builder.WriteString("4. 是否存在安全风险或性能问题\n")
	builder.WriteString("5. 代码变更是否符合项目的架构和设计原则\n\n")

	return builder.String()
}

// calculateFileImportance 计算文件重要性
func (ecr *EnhancedCodeReviewer) calculateFileImportance(contextMap map[string]*ContextInfo, changesText string) map[string]float64 {
	importance := make(map[string]float64)

	for filePath, context := range contextMap {
		score := 1.0 // 基础分数

		// 根据变更内容中的文件路径出现频率加权
		if strings.Count(changesText, filePath) > 0 {
			score += 0.5
		}

		// 根据函数数量加权（更多函数=更重要）
		if len(context.Functions) > 0 {
			score += float64(len(context.Functions)) * 0.1
		}

		// 根据文件类型加权
		if strings.HasSuffix(filePath, ".go") {
			score += 0.3 // Go文件优先级高
		}
		if strings.Contains(filePath, "service") || strings.Contains(filePath, "handler") {
			score += 0.4 // 业务逻辑文件优先级高
		}
		if strings.Contains(filePath, "test") {
			score -= 0.2 // 测试文件优先级较低
		}

		// 根据导入数量加权（更多导入=更核心）
		if len(context.Imports) > 5 {
			score += 0.2
		}

		importance[filePath] = score
	}

	return importance
}

// sortFilesByImportance 按重要性排序文件
func (ecr *EnhancedCodeReviewer) sortFilesByImportance(contextMap map[string]*ContextInfo, importanceMap map[string]float64) []string {
	files := make([]string, 0, len(contextMap))
	for filePath := range contextMap {
		files = append(files, filePath)
	}

	// 按重要性降序排序
	sort.Slice(files, func(i, j int) bool {
		return importanceMap[files[i]] > importanceMap[files[j]]
	})

	return files
}

// addDynamicContextInfo 动态添加上下文信息 - 针对80000 tokens优化
func (ecr *EnhancedCodeReviewer) addDynamicContextInfo(builder *strings.Builder, context *ContextInfo, tokenBudget int) int {
	tokensUsed := 0

	// 计算各部分预估token消耗
	importsTokens := len(context.Imports) * 10
	functionsTokens := len(context.Functions) * 15
	classesTokens := len(context.Classes) * 12

	totalEstimated := importsTokens + functionsTokens + classesTokens

	// 80000 tokens下，更宽松的显示策略
	if totalEstimated > tokenBudget {
		ratio := float64(tokenBudget) / float64(totalEstimated)
		maxImports := int(float64(len(context.Imports)) * ratio)
		maxFunctions := int(float64(len(context.Functions)) * ratio)
		maxClasses := int(float64(len(context.Classes)) * ratio)

		// 80000 tokens下，提高最少显示数量
		if maxImports < 10 && len(context.Imports) > 0 {
			maxImports = min(10, len(context.Imports))
		}
		if maxFunctions < 15 && len(context.Functions) > 0 {
			maxFunctions = min(15, len(context.Functions))
		}
		if maxClasses < 10 && len(context.Classes) > 0 {
			maxClasses = min(10, len(context.Classes))
		}

		tokensUsed += ecr.addImportsInfo(builder, context.Imports, maxImports)
		tokensUsed += ecr.addFunctionsInfo(builder, context.Functions, maxFunctions)
		tokensUsed += ecr.addClassesInfo(builder, context.Classes, maxClasses)
	} else {
		// 80000 tokens下，可以显示更多信息
		maxImports := min(50, len(context.Imports))     // 从20增加到50
		maxFunctions := min(50, len(context.Functions)) // 从20增加到50
		maxClasses := min(30, len(context.Classes))     // 从20增加到30

		tokensUsed += ecr.addImportsInfo(builder, context.Imports, maxImports)
		tokensUsed += ecr.addFunctionsInfo(builder, context.Functions, maxFunctions)
		tokensUsed += ecr.addClassesInfo(builder, context.Classes, maxClasses)
	}

	return tokensUsed
}

// addImportsInfo 添加导入信息
func (ecr *EnhancedCodeReviewer) addImportsInfo(builder *strings.Builder, imports []string, maxShow int) int {
	if len(imports) == 0 {
		return 0
	}

	builder.WriteString("**关键依赖：**\n")
	tokensUsed := 20 // "**关键依赖：**\n" 的估算token

	shown := 0
	for i, imp := range imports {
		if shown >= maxShow {
			break
		}
		line := fmt.Sprintf("- %s\n", imp)
		builder.WriteString(line)
		tokensUsed += len(line) / 3 // 粗略估算
		shown++

		if i >= maxShow-1 && len(imports) > maxShow {
			remaining := fmt.Sprintf("- ... (还有%d个导入)\n", len(imports)-maxShow)
			builder.WriteString(remaining)
			tokensUsed += len(remaining) / 3
			break
		}
	}
	builder.WriteString("\n")
	tokensUsed += 1

	return tokensUsed
}

// addFunctionsInfo 添加函数信息
func (ecr *EnhancedCodeReviewer) addFunctionsInfo(builder *strings.Builder, functions []string, maxShow int) int {
	if len(functions) == 0 {
		return 0
	}

	builder.WriteString("**核心函数：**\n")
	tokensUsed := 20

	shown := 0
	for i, fn := range functions {
		if shown >= maxShow {
			break
		}
		line := fmt.Sprintf("- %s()\n", fn)
		builder.WriteString(line)
		tokensUsed += len(line) / 3
		shown++

		if i >= maxShow-1 && len(functions) > maxShow {
			remaining := fmt.Sprintf("- ... (还有%d个函数)\n", len(functions)-maxShow)
			builder.WriteString(remaining)
			tokensUsed += len(remaining) / 3
			break
		}
	}
	builder.WriteString("\n")
	tokensUsed += 1

	return tokensUsed
}

// addClassesInfo 添加类型信息
func (ecr *EnhancedCodeReviewer) addClassesInfo(builder *strings.Builder, classes []string, maxShow int) int {
	if len(classes) == 0 {
		return 0
	}

	builder.WriteString("**数据类型：**\n")
	tokensUsed := 20

	shown := 0
	for i, cls := range classes {
		if shown >= maxShow {
			break
		}
		line := fmt.Sprintf("- %s\n", cls)
		builder.WriteString(line)
		tokensUsed += len(line) / 3
		shown++

		if i >= maxShow-1 && len(classes) > maxShow {
			remaining := fmt.Sprintf("- ... (还有%d个类型)\n", len(classes)-maxShow)
			builder.WriteString(remaining)
			tokensUsed += len(remaining) / 3
			break
		}
	}
	builder.WriteString("\n")
	tokensUsed += 1

	return tokensUsed
}

// generateBusinessLogicHints 生成业务逻辑分析提示 - 80000 tokens深度版本
func (ecr *EnhancedCodeReviewer) generateBusinessLogicHints(contextMap map[string]*ContextInfo, changesText string) string {
	var hints strings.Builder

	hints.WriteString("基于80000 tokens的超大上下文，请重点关注以下业务逻辑分析维度：\n\n")

	// 深度分析变更模式，生成针对性提示
	if strings.Contains(changesText, "if ") && strings.Contains(changesText, "error") {
		hints.WriteString("🔍 **错误处理深度分析**:\n")
		hints.WriteString("- 检查错误处理的完整性：是否覆盖所有可能的异常情况\n")
		hints.WriteString("- 验证错误传播链：错误是否正确向上传播并被适当处理\n")
		hints.WriteString("- 分析错误恢复机制：系统是否能从错误状态中恢复\n")
		hints.WriteString("- 检查错误日志：是否记录了足够的调试信息\n\n")
	}

	if strings.Contains(changesText, "func ") || strings.Contains(changesText, "function") {
		hints.WriteString("🔍 **函数逻辑深度分析**:\n")
		hints.WriteString("- 参数验证完整性：检查所有输入参数的边界条件和类型验证\n")
		hints.WriteString("- 返回值一致性：确保在所有执行路径下返回值的类型和含义一致\n")
		hints.WriteString("- 副作用控制：分析函数是否有未声明的副作用或状态修改\n")
		hints.WriteString("- 幂等性检查：对于应该幂等的操作，验证重复调用的安全性\n")
		hints.WriteString("- 并发安全性：在多线程环境下的行为是否正确\n\n")
	}

	if strings.Contains(changesText, "database") || strings.Contains(changesText, "db.") || strings.Contains(changesText, "sql") {
		hints.WriteString("🔍 **数据操作深度分析**:\n")
		hints.WriteString("- 事务完整性：检查事务边界是否正确，是否有未提交或未回滚的事务\n")
		hints.WriteString("- 数据一致性：验证数据修改是否保持业务规则的一致性\n")
		hints.WriteString("- 并发控制：检查是否正确处理数据库锁和并发访问\n")
		hints.WriteString("- SQL注入防护：验证所有用户输入是否经过适当的转义和验证\n")
		hints.WriteString("- 性能影响：分析查询效率，是否存在N+1问题或缺少索引\n")
		hints.WriteString("- 数据迁移安全：如涉及schema变更，检查向后兼容性\n\n")
	}

	if strings.Contains(changesText, "http") || strings.Contains(changesText, "request") || strings.Contains(changesText, "response") {
		hints.WriteString("🔍 **API接口深度分析**:\n")
		hints.WriteString("- 输入验证策略：检查请求参数的完整验证和过滤机制\n")
		hints.WriteString("- 权限控制粒度：验证API的访问控制是否足够细粒度和安全\n")
		hints.WriteString("- 响应格式一致性：确保API响应格式符合约定和标准\n")
		hints.WriteString("- 错误码标准化：检查HTTP状态码和错误响应的标准化\n")
		hints.WriteString("- 幂等性设计：对于非查询操作，验证幂等性设计\n")
		hints.WriteString("- 限流和防护：检查是否有适当的限流和防攻击机制\n\n")
	}

	if strings.Contains(changesText, "for ") || strings.Contains(changesText, "while") || strings.Contains(changesText, "range") {
		hints.WriteString("🔍 **循环逻辑深度分析**:\n")
		hints.WriteString("- 边界条件验证：检查循环的开始、结束条件是否正确\n")
		hints.WriteString("- 无限循环风险：分析是否存在无限循环的可能性\n")
		hints.WriteString("- 性能影响评估：计算循环的时间复杂度和空间复杂度\n")
		hints.WriteString("- 内存泄漏检查：验证循环内是否有内存或资源泄漏\n")
		hints.WriteString("- 中断处理：检查循环是否能正确响应中断信号\n\n")
	}

	// 深度分析文件类型相关的业务提示
	hasServiceFile := false
	hasHandlerFile := false
	hasRepositoryFile := false
	hasConfigFile := false
	hasTestFile := false

	for filePath := range contextMap {
		if strings.Contains(filePath, "service") {
			hasServiceFile = true
		}
		if strings.Contains(filePath, "handler") || strings.Contains(filePath, "controller") {
			hasHandlerFile = true
		}
		if strings.Contains(filePath, "repository") || strings.Contains(filePath, "dao") {
			hasRepositoryFile = true
		}
		if strings.Contains(filePath, "config") || strings.Contains(filePath, ".yaml") || strings.Contains(filePath, ".json") {
			hasConfigFile = true
		}
		if strings.Contains(filePath, "test") || strings.Contains(filePath, "_test") {
			hasTestFile = true
		}
	}

	if hasServiceFile {
		hints.WriteString("🏗️ **业务服务层深度分析**:\n")
		hints.WriteString("- 业务逻辑封装：检查业务逻辑是否正确封装，避免逻辑泄漏\n")
		hints.WriteString("- 服务边界清晰：验证服务职责是否单一，边界是否清晰\n")
		hints.WriteString("- 依赖注入正确：检查依赖关系是否合理，避免循环依赖\n")
		hints.WriteString("- 异常处理策略：分析业务异常的处理和传播策略\n\n")
	}

	if hasHandlerFile {
		hints.WriteString("🌐 **接口处理层深度分析**:\n")
		hints.WriteString("- 请求处理流程：检查请求从接收到响应的完整流程\n")
		hints.WriteString("- 参数绑定安全：验证请求参数绑定的安全性和正确性\n")
		hints.WriteString("- 权限验证层次：检查权限验证的层次和完整性\n")
		hints.WriteString("- 响应处理统一：确保响应处理的统一性和标准化\n\n")
	}

	if hasRepositoryFile {
		hints.WriteString("💾 **数据访问层深度分析**:\n")
		hints.WriteString("- 数据访问模式：检查数据访问模式是否符合最佳实践\n")
		hints.WriteString("- 查询优化：分析数据库查询的效率和优化空间\n")
		hints.WriteString("- 连接管理：检查数据库连接的获取、使用和释放\n")
		hints.WriteString("- 缓存策略：评估数据缓存的策略和一致性\n\n")
	}

	if hasConfigFile {
		hints.WriteString("⚙️ **配置管理深度分析**:\n")
		hints.WriteString("- 配置变更影响：分析配置变更对系统行为的影响\n")
		hints.WriteString("- 环境隔离：检查不同环境配置的隔离和管理\n")
		hints.WriteString("- 敏感信息保护：验证敏感配置信息的保护措施\n")
		hints.WriteString("- 动态配置支持：检查是否支持动态配置更新\n\n")
	}

	if hasTestFile {
		hints.WriteString("🧪 **测试代码深度分析**:\n")
		hints.WriteString("- 测试覆盖完整性：检查测试用例是否覆盖所有业务场景\n")
		hints.WriteString("- 边界条件测试：验证是否包含边界条件和异常情况的测试\n")
		hints.WriteString("- 测试数据管理：检查测试数据的管理和清理策略\n")
		hints.WriteString("- 性能测试考虑：评估是否需要性能和压力测试\n\n")
	}

	// 添加架构层面的分析提示
	hints.WriteString("🏛️ **架构设计深度分析**:\n")
	hints.WriteString("- 模块职责分离：检查代码变更是否符合单一职责原则\n")
	hints.WriteString("- 接口设计合理：验证接口设计是否符合开闭原则\n")
	hints.WriteString("- 扩展性考虑：分析代码的可扩展性和未来维护成本\n")
	hints.WriteString("- 性能影响评估：评估变更对系统整体性能的影响\n\n")

	// 添加安全性分析提示
	hints.WriteString("🔐 **安全性深度分析**:\n")
	hints.WriteString("- 输入验证防护：检查所有外部输入的验证和过滤\n")
	hints.WriteString("- 权限控制细粒度：验证权限控制的细粒度和完整性\n")
	hints.WriteString("- 数据脱敏处理：检查敏感数据的处理和保护措施\n")
	hints.WriteString("- 日志安全考虑：验证日志记录是否泄露敏感信息\n\n")

	if hints.Len() == 0 {
		hints.WriteString("🔍 **通用深度分析**: 基于80000 tokens上下文，重点关注代码的逻辑正确性、异常处理完整性、性能影响和安全风险\n\n")
	}

	return hints.String()
}

// getFileContentWithPathCorrection 智能路径修正获取文件内容 - 借鉴PR-Agent方法
func (ecr *EnhancedCodeReviewer) getFileContentWithPathCorrection(filePath, branch string) (string, error) {
	logger.Infof("尝试原始路径: %s", filePath)

	// 1. 首先尝试原始路径
	content, err := ecr.contextExtractor.GetFileContentFromGitLabSimple(filePath, branch)
	if err == nil {
		logger.Infof("✅ 原始路径成功: %s", filePath)
		return content, nil
	}
	logger.Warnf("❌ 原始路径失败: %s, 错误: %v", filePath, err)

	// 2. 分析路径结构，借鉴PR-Agent的路径压缩策略
	pathParts := strings.Split(filePath, "/")
	if len(pathParts) < 2 {
		return "", fmt.Errorf("路径格式不正确: %s", filePath)
	}

	// 3. 检测多模块项目模式
	possibleModuleName := pathParts[0]
	logger.Infof("检测到可能的模块名: %s", possibleModuleName)

	// 4. 尝试去掉模块名的路径（PR-Agent的压缩策略）
	withoutModulePath := strings.Join(pathParts[1:], "/")
	logger.Infof("尝试去掉模块名的路径: %s", withoutModulePath)

	content, err = ecr.contextExtractor.GetFileContentFromGitLabSimple(withoutModulePath, branch)
	if err == nil {
		logger.Infof("✅ 修正路径成功: %s", withoutModulePath)
		return content, nil
	}
	logger.Warnf("❌ 修正路径失败: %s", withoutModulePath)

	// 5. 尝试在根目录下的模块子目录中查找（类似PR-Agent的动态上下文）
	moduleSubPath := fmt.Sprintf("%s/%s", possibleModuleName, withoutModulePath)
	logger.Infof("尝试模块子目录路径: %s", moduleSubPath)

	content, err = ecr.contextExtractor.GetFileContentFromGitLabSimple(moduleSubPath, branch)
	if err == nil {
		logger.Infof("✅ 模块子目录路径成功: %s", moduleSubPath)
		return content, nil
	}
	logger.Warnf("❌ 模块子目录路径失败: %s", moduleSubPath)

	// 6. 智能搜索策略 - 借鉴PR-Agent的文件发现机制
	fileName := pathParts[len(pathParts)-1]
	logger.Infof("尝试智能搜索文件: %s", fileName)

	// 在常见的Java项目结构中搜索
	commonPaths := []string{
		fmt.Sprintf("src/main/java/%s", strings.Join(pathParts[1:], "/")),
		fmt.Sprintf("%s/src/main/java/%s", possibleModuleName, strings.Join(pathParts[2:], "/")),
		fmt.Sprintf("src/%s", strings.Join(pathParts[1:], "/")),
	}

	for _, searchPath := range commonPaths {
		logger.Infof("尝试常见路径: %s", searchPath)
		content, err = ecr.contextExtractor.GetFileContentFromGitLabSimple(searchPath, branch)
		if err == nil {
			logger.Infof("✅ 常见路径成功: %s", searchPath)
			return content, nil
		}
	}

	// 7. 最后的尝试：在仓库中搜索同名文件
	logger.Infof("尝试在仓库中搜索同名文件: %s", fileName)
	foundPath, err := ecr.searchFileInRepository(fileName, branch)
	if err == nil && foundPath != "" {
		content, err = ecr.contextExtractor.GetFileContentFromGitLabSimple(foundPath, branch)
		if err == nil {
			logger.Infof("✅ 仓库搜索成功: %s", foundPath)
			return content, nil
		}
	}

	logger.Errorf("❌ 所有路径修正尝试都失败了: %s", filePath)
	return "", fmt.Errorf("无法找到文件 %s，已尝试多种路径修正方案", filePath)
}

// searchFileInRepository 在仓库中搜索文件 - 借鉴PR-Agent的文件发现机制
func (ecr *EnhancedCodeReviewer) searchFileInRepository(fileName, branch string) (string, error) {
	// 获取仓库根目录结构
	tree, err := ecr.contextExtractor.gitlabClient.ListRepositoryTree(branch, "")
	if err != nil {
		return "", fmt.Errorf("无法获取仓库结构: %w", err)
	}

	// 递归搜索文件
	return ecr.searchFileRecursively(fileName, branch, "", tree, 0, 3)
}

// searchFileRecursively 递归搜索文件（限制深度避免无限递归）
func (ecr *EnhancedCodeReviewer) searchFileRecursively(fileName, branch, currentPath string, tree []map[string]interface{}, depth, maxDepth int) (string, error) {
	if depth > maxDepth {
		return "", fmt.Errorf("搜索深度超限")
	}

	for _, item := range tree {
		itemName, ok := item["name"].(string)
		if !ok {
			continue
		}

		itemType, ok := item["type"].(string)
		if !ok {
			continue
		}

		fullPath := itemName
		if currentPath != "" {
			fullPath = currentPath + "/" + itemName
		}

		if itemType == "blob" && itemName == fileName {
			// 找到文件
			return fullPath, nil
		} else if itemType == "tree" && depth < maxDepth {
			// 递归搜索子目录
			subTree, err := ecr.contextExtractor.gitlabClient.ListRepositoryTree(branch, fullPath)
			if err != nil {
				continue // 忽略错误，继续搜索
			}

			result, err := ecr.searchFileRecursively(fileName, branch, fullPath, subTree, depth+1, maxDepth)
			if err == nil {
				return result, nil
			}
		}
	}

	return "", fmt.Errorf("文件未找到")
}

// 增强诊断功能 - 借鉴PR-Agent的调试能力
func (ecr *EnhancedCodeReviewer) diagnoseFailedFiles(changes []repository.GitLabChange, targetBranch string) {
	logger.Errorf("警告：所有文件的上下文收集都失败了！开始详细诊断...")
	logger.Errorf("目标分支: %s", targetBranch)

	// 分析失败的文件路径
	logger.Errorf("失败的文件路径分析:")
	for i, change := range changes {
		if change.DeletedFile {
			continue
		}

		filePath := change.NewPath
		if filePath == "" {
			filePath = change.OldPath
		}

		logger.Errorf("  文件 #%d: %s", i+1, filePath)

		// 路径分析
		pathParts := strings.Split(filePath, "/")
		logger.Errorf("    路径层级: %v", pathParts)

		if len(pathParts) > 1 {
			logger.Errorf("    可能的模块名: %s", pathParts[0])
		}
	}

	// 检查分支结构 - 借鉴PR-Agent的动态上下文分析
	logger.Errorf("正在检查分支 '%s' 的文件结构...", targetBranch)
	if tree, err := ecr.contextExtractor.gitlabClient.ListRepositoryTree(targetBranch, ""); err == nil {
		logger.Infof("分支 %s 的根目录结构：", targetBranch)

		// 显示目录和文件结构
		directories := make([]string, 0)
		files := make([]string, 0)

		for _, item := range tree {
			if path, ok := item["path"].(string); ok {
				if itemType, ok := item["type"].(string); ok {
					if itemType == "tree" {
						directories = append(directories, path)
					} else {
						files = append(files, path)
					}
				}
			}
		}

		// 显示目录结构
		logger.Infof("  目录 (%d个):", len(directories))
		for i, dir := range directories {
			if i < 20 {
				logger.Infof("    📁 %s", dir)
			} else {
				logger.Infof("    ... 还有 %d 个目录", len(directories)-20)
				break
			}
		}

		// 显示根目录文件
		logger.Infof("  根目录文件 (%d个):", len(files))
		for i, file := range files {
			if i < 10 {
				logger.Infof("    📄 %s", file)
			} else {
				logger.Infof("    ... 还有 %d 个文件", len(files)-10)
				break
			}
		}

		// 检查失败文件的模块是否存在
		logger.Infof("🔍 检查失败文件的模块目录:")
		moduleNames := make(map[string]bool)
		for _, change := range changes {
			if change.DeletedFile {
				continue
			}
			filePath := change.NewPath
			if filePath == "" {
				filePath = change.OldPath
			}
			if strings.Contains(filePath, "/") {
				parts := strings.Split(filePath, "/")
				if len(parts) > 1 {
					moduleName := parts[0]
					if !moduleNames[moduleName] {
						moduleNames[moduleName] = true
						found := false
						for _, dir := range directories {
							if dir == moduleName {
								found = true
								break
							}
						}
						if found {
							logger.Infof("  ✅ 模块目录存在: %s", moduleName)
						} else {
							logger.Errorf("  ❌ 模块目录不存在: %s", moduleName)
						}
					}
				}
			}
		}

		// 多模块项目检查
		logger.Infof("多模块项目检查:")
		hasMultiModule := false
		for _, dir := range directories {
			if strings.HasSuffix(dir, "-service") || strings.HasSuffix(dir, "-core") ||
				strings.HasSuffix(dir, "-api") || strings.HasSuffix(dir, "-web") ||
				strings.HasSuffix(dir, "-common") || strings.HasSuffix(dir, "-util") {
				logger.Infof("  🔍 发现可能的模块目录: %s", dir)
				hasMultiModule = true
			}
		}

		if hasMultiModule {
			logger.Errorf("🚨 检测到多模块项目结构！")
			logger.Errorf("   问题可能是：GitLab API返回的文件路径包含了模块名，但实际文件可能在子模块中")
			logger.Errorf("   建议检查各个子模块的文件结构")
		}

	} else {
		logger.Errorf("无法获取分支文件结构: %v", err)
	}

	// 提供解决方案建议 - 借鉴PR-Agent的用户友好提示
	logger.Errorf("🔧 可能的解决方案：")
	logger.Errorf("1. 检查文件是否真的存在于分支 '%s' 中", targetBranch)
	logger.Errorf("2. 如果是多模块项目，可能需要调整文件路径解析逻辑")
	logger.Errorf("3. 检查GitLab权限是否允许访问这些文件")
	logger.Errorf("4. 确认commit变更中的文件路径格式是否正确")
}
