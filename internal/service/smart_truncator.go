package service

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"ai-codereview-service/internal/llm"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

// SmartTruncator 智能截断器
type SmartTruncator struct {
	maxTokens int
}

// NewSmartTruncator 创建智能截断器
func NewSmartTruncator(maxTokens int) *SmartTruncator {
	return &SmartTruncator{
		maxTokens: maxTokens,
	}
}

// TruncateWithContext 智能截断，保留与变更相关的上下文
func (st *SmartTruncator) TruncateWithContext(fileContent string, change repository.GitLabChange, filePath string) string {
	if llm.CountTokens(fileContent) <= st.maxTokens {
		return fileContent
	}

	logger.Infof("文件 %s 内容过长，开始智能截断...", filePath)

	// 解析diff，获取变更的行号范围
	changedLines := st.parseChangedLines(change.Diff)
	if len(changedLines) == 0 {
		logger.Warn("无法解析变更行号，使用简单截断")
		return llm.TruncateByTokens(fileContent, st.maxTokens)
	}

	// 根据文件类型选择截断策略
	ext := strings.ToLower(strings.Split(filePath, ".")[len(strings.Split(filePath, "."))-1])
	switch ext {
	case "go":
		return st.truncateGoFile(fileContent, changedLines)
	case "py":
		return st.truncatePythonFile(fileContent, changedLines)
	case "java":
		return st.truncateJavaFile(fileContent, changedLines)
	case "js", "ts":
		return st.truncateJSFile(fileContent, changedLines)
	default:
		return st.truncateGenericFile(fileContent, changedLines)
	}
}

// parseChangedLines 解析diff中的变更行号
func (st *SmartTruncator) parseChangedLines(diff string) []int {
	var changedLines []int

	// 解析diff头部信息，如: @@ -10,7 +10,12 @@
	re := regexp.MustCompile(`@@\s+-(\d+),?\d*\s+\+(\d+),?\d*\s+@@`)
	matches := re.FindAllStringSubmatch(diff, -1)

	for _, match := range matches {
		if len(match) >= 3 {
			if startLine, err := strconv.Atoi(match[2]); err == nil {
				// 解析具体的变更行
				lines := strings.Split(diff, "\n")
				currentLine := startLine

				for _, line := range lines {
					if strings.HasPrefix(line, "+") || strings.HasPrefix(line, "-") {
						changedLines = append(changedLines, currentLine)
					}
					if !strings.HasPrefix(line, "-") {
						currentLine++
					}
				}
			}
		}
	}

	// 去重并排序
	uniqueLines := make(map[int]bool)
	for _, line := range changedLines {
		uniqueLines[line] = true
	}

	result := make([]int, 0, len(uniqueLines))
	for line := range uniqueLines {
		result = append(result, line)
	}
	sort.Ints(result)

	return result
}

// truncateGoFile Go文件的智能截断 - 增强版
func (st *SmartTruncator) truncateGoFile(fileContent string, changedLines []int) string {
	// 使用AST解析Go文件
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, "", fileContent, parser.ParseComments)
	if err != nil {
		logger.Warnf("Go文件AST解析失败，使用通用截断: %v", err)
		return st.truncateGenericFile(fileContent, changedLines)
	}

	lines := strings.Split(fileContent, "\n")
	importantRanges := []LineRange{}
	changedFunctions := make(map[string]bool) // 记录变更的函数名

	logger.Infof("Go文件智能截断: 变更行数=%d, 文件总行数=%d", len(changedLines), len(lines))

	// 1. 保留包声明和导入（完整保留）
	if node.Package.IsValid() {
		importantRanges = append(importantRanges, LineRange{1, 1})
	}

	// 保留所有导入语句
	for _, imp := range node.Imports {
		start := fset.Position(imp.Pos()).Line
		end := fset.Position(imp.End()).Line
		importantRanges = append(importantRanges, LineRange{start, end})
	}

	// 2. 保留所有全局变量和常量定义
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.GenDecl:
			// 保留var和const声明
			if x.Tok == token.VAR || x.Tok == token.CONST {
				start := fset.Position(x.Pos()).Line
				end := fset.Position(x.End()).Line
				importantRanges = append(importantRanges, LineRange{start, end})
				logger.Debugf("保留全局声明: 行%d-%d", start, end)
			}
		}
		return true
	})

	// 3. 找到包含变更行的函数/类型定义，并记录函数调用关系
	functionCallMap := make(map[string][]string) // 函数名 -> 被调用的函数列表

	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.FuncDecl:
			start := fset.Position(x.Pos()).Line
			end := fset.Position(x.End()).Line
			funcName := ""
			if x.Name != nil {
				funcName = x.Name.Name
			}

			// 如果函数包含变更，记录并保留
			if st.containsChangedLines(start, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{start, end})
				changedFunctions[funcName] = true
				logger.Infof("发现变更函数: %s (行%d-%d)", funcName, start, end)

				// 分析函数内的调用关系
				if x.Body != nil {
					calls := st.extractFunctionCalls(x.Body)
					functionCallMap[funcName] = calls
					logger.Debugf("函数 %s 调用了: %v", funcName, calls)
				}
			}
		case *ast.TypeSpec:
			start := fset.Position(x.Pos()).Line
			end := fset.Position(x.End()).Line
			if st.containsChangedLines(start, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{start, end})
				logger.Infof("发现变更类型: %s (行%d-%d)", x.Name.Name, start, end)
			}
		}
		return true
	})

	// 4. 保留被变更函数调用的相关函数（依赖分析）
	ast.Inspect(node, func(n ast.Node) bool {
		if x, ok := n.(*ast.FuncDecl); ok && x.Name != nil {
			funcName := x.Name.Name
			start := fset.Position(x.Pos()).Line
			end := fset.Position(x.End()).Line

			// 如果这个函数被变更函数调用，也保留它
			for changedFunc := range changedFunctions {
				if calls, exists := functionCallMap[changedFunc]; exists {
					for _, calledFunc := range calls {
						if calledFunc == funcName {
							importantRanges = append(importantRanges, LineRange{start, end})
							logger.Infof("保留依赖函数: %s (被%s调用, 行%d-%d)", funcName, changedFunc, start, end)
							break
						}
					}
				}
			}
		}
		return true
	})

	// 5. 添加变更行周围的扩展上下文
	for _, line := range changedLines {
		contextStart := max(1, line-30) // 增加到30行上下文
		contextEnd := min(len(lines), line+30)
		importantRanges = append(importantRanges, LineRange{contextStart, contextEnd})
	}

	result := st.extractImportantLines(lines, importantRanges)
	logger.Infof("Go文件截断完成: 原始%d行 -> 保留重要部分", len(lines))
	return result
}

// extractFunctionCalls 从AST节点中提取函数调用
func (st *SmartTruncator) extractFunctionCalls(node ast.Node) []string {
	var calls []string

	ast.Inspect(node, func(n ast.Node) bool {
		if callExpr, ok := n.(*ast.CallExpr); ok {
			if ident, ok := callExpr.Fun.(*ast.Ident); ok {
				// 简单函数调用，如: funcName()
				calls = append(calls, ident.Name)
			} else if selExpr, ok := callExpr.Fun.(*ast.SelectorExpr); ok {
				// 方法调用，如: obj.method()
				calls = append(calls, selExpr.Sel.Name)
			}
		}
		return true
	})

	// 去重
	uniqueCalls := make(map[string]bool)
	result := []string{}
	for _, call := range calls {
		if !uniqueCalls[call] && call != "" {
			uniqueCalls[call] = true
			result = append(result, call)
		}
	}

	return result
}

// truncatePythonFile Python文件的智能截断
func (st *SmartTruncator) truncatePythonFile(fileContent string, changedLines []int) string {
	return st.truncateGenericFile(fileContent, changedLines)
}

// truncateJavaFile Java文件的智能截断 - 针对Dubbo框架优化
func (st *SmartTruncator) truncateJavaFile(fileContent string, changedLines []int) string {
	lines := strings.Split(fileContent, "\n")
	importantRanges := []LineRange{}

	// 1. 保留包声明和导入语句（特别关注Dubbo相关导入）
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		if strings.HasPrefix(trimmed, "package ") ||
			strings.HasPrefix(trimmed, "import ") ||
			strings.Contains(trimmed, "@") { // 保留注解行
			importantRanges = append(importantRanges, LineRange{i + 1, i + 1})
		}
	}

	// 2. 找到包含变更的类、方法、接口定义
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)

		// 检测类、接口、枚举定义
		if st.isJavaClassDefinition(trimmed) {
			end := st.findJavaBlockEnd(lines, i)
			if st.containsChangedLines(i+1, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{i + 1, end})
			}
		}

		// 检测方法定义（包括Dubbo服务方法）
		if st.isJavaMethodDefinition(trimmed) {
			end := st.findJavaBlockEnd(lines, i)
			if st.containsChangedLines(i+1, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{i + 1, end})
			}
		}

		// 检测Dubbo特有的注解和配置
		if st.isDubboAnnotation(trimmed) {
			// 保留注解及其下面的方法/类
			end := st.findAnnotatedElementEnd(lines, i)
			importantRanges = append(importantRanges, LineRange{i + 1, end})
		}
	}

	// 3. 添加变更行周围的扩展上下文
	for _, line := range changedLines {
		contextStart := max(1, line-30) // 与Go文件保持一致，30行上下文
		contextEnd := min(len(lines), line+30)
		importantRanges = append(importantRanges, LineRange{contextStart, contextEnd})
	}

	return st.extractImportantLines(lines, importantRanges)
}

// truncateJSFile JavaScript/TypeScript文件的智能截断 - 针对React/Vue优化
func (st *SmartTruncator) truncateJSFile(fileContent string, changedLines []int) string {
	lines := strings.Split(fileContent, "\n")
	importantRanges := []LineRange{}

	// 1. 保留导入语句（ES6 import, CommonJS require, Vue/React相关导入）
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		if st.isJSImportStatement(trimmed) {
			importantRanges = append(importantRanges, LineRange{i + 1, i + 1})
		}
	}

	// 2. 找到包含变更的函数、组件、类定义
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)

		// React组件定义
		if st.isReactComponent(trimmed) {
			end := st.findJSBlockEnd(lines, i)
			if st.containsChangedLines(i+1, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{i + 1, end})
			}
		}

		// Vue组件定义
		if st.isVueComponent(trimmed) {
			end := st.findVueComponentEnd(lines, i)
			if st.containsChangedLines(i+1, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{i + 1, end})
			}
		}

		// 普通函数定义
		if st.isJSFunctionDefinition(trimmed) {
			end := st.findJSBlockEnd(lines, i)
			if st.containsChangedLines(i+1, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{i + 1, end})
			}
		}

		// Hook定义（React）
		if st.isReactHook(trimmed) {
			end := st.findJSBlockEnd(lines, i)
			if st.containsChangedLines(i+1, end, changedLines) {
				importantRanges = append(importantRanges, LineRange{i + 1, end})
			}
		}
	}

	// 3. 添加变更行周围的扩展上下文
	for _, line := range changedLines {
		contextStart := max(1, line-30) // 与Go文件保持一致，30行上下文
		contextEnd := min(len(lines), line+30)
		importantRanges = append(importantRanges, LineRange{contextStart, contextEnd})
	}

	return st.extractImportantLines(lines, importantRanges)
}

// truncateGenericFile 通用文件的智能截断 - 80000 tokens优化
func (st *SmartTruncator) truncateGenericFile(fileContent string, changedLines []int) string {
	lines := strings.Split(fileContent, "\n")
	importantRanges := []LineRange{}

	// 为每个变更行添加上下文
	for _, line := range changedLines {
		contextStart := max(1, line-40) // 从20行增加到40行上下文窗口
		contextEnd := min(len(lines), line+40)
		importantRanges = append(importantRanges, LineRange{contextStart, contextEnd})
	}

	return st.extractImportantLines(lines, importantRanges)
}

// LineRange 行范围
type LineRange struct {
	Start int
	End   int
}

// containsChangedLines 检查范围是否包含变更行
func (st *SmartTruncator) containsChangedLines(start, end int, changedLines []int) bool {
	for _, line := range changedLines {
		if line >= start && line <= end {
			return true
		}
	}
	return false
}

// extractImportantLines 提取重要行并合并重叠范围
func (st *SmartTruncator) extractImportantLines(lines []string, ranges []LineRange) string {
	if len(ranges) == 0 {
		return llm.TruncateByTokens(strings.Join(lines, "\n"), st.maxTokens)
	}

	// 合并重叠的范围
	mergedRanges := st.mergeRanges(ranges)

	var result strings.Builder
	totalTokens := 0

	for i, r := range mergedRanges {
		// 添加范围标识
		if i > 0 {
			result.WriteString("\n... [省略部分代码] ...\n\n")
		}

		// 提取范围内的行
		for lineNum := r.Start; lineNum <= r.End && lineNum <= len(lines); lineNum++ {
			line := fmt.Sprintf("%d: %s\n", lineNum, lines[lineNum-1])
			lineTokens := llm.CountTokens(line)

			if totalTokens+lineTokens > st.maxTokens {
				result.WriteString("... [内容过长，已截断] ...")
				break
			}

			result.WriteString(line)
			totalTokens += lineTokens
		}
	}

	return result.String()
}

// mergeRanges 合并重叠的行范围
func (st *SmartTruncator) mergeRanges(ranges []LineRange) []LineRange {
	if len(ranges) == 0 {
		return ranges
	}

	// 按起始行排序
	sort.Slice(ranges, func(i, j int) bool {
		return ranges[i].Start < ranges[j].Start
	})

	merged := []LineRange{ranges[0]}

	for i := 1; i < len(ranges); i++ {
		current := ranges[i]
		last := &merged[len(merged)-1]

		// 如果当前范围与上一个范围重叠或相邻，则合并
		if current.Start <= last.End+1 {
			if current.End > last.End {
				last.End = current.End
			}
		} else {
			merged = append(merged, current)
		}
	}

	return merged
}

// 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Java相关的辅助检测方法
func (st *SmartTruncator) isJavaClassDefinition(line string) bool {
	patterns := []string{
		`(public|private|protected)?\s*(abstract|final)?\s*(class|interface|enum)\s+\w+`,
		`@\w+.*class\s+\w+`, // 带注解的类
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) isJavaMethodDefinition(line string) bool {
	patterns := []string{
		`(public|private|protected)\s+.*\s+\w+\s*\([^)]*\)\s*\{?`,
		`@\w+.*\s+\w+\s*\([^)]*\)`, // 带注解的方法
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) isDubboAnnotation(line string) bool {
	dubboAnnotations := []string{
		"@Service", "@Reference", "@Consumer", "@Provider",
		"@DubboService", "@DubboReference", "@DubboConsumer",
		"@EnableDubbo", "@DubboComponentScan",
	}

	for _, annotation := range dubboAnnotations {
		if strings.Contains(line, annotation) {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) findAnnotatedElementEnd(lines []string, start int) int {
	// 找到注解后面的类或方法的结束位置
	for i := start + 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" || strings.HasPrefix(line, "//") {
			continue
		}

		if st.isJavaClassDefinition(line) || st.isJavaMethodDefinition(line) {
			return st.findJavaBlockEnd(lines, i)
		}
	}
	return start + 1
}

// JavaScript/React/Vue相关的辅助检测方法
func (st *SmartTruncator) isJSImportStatement(line string) bool {
	patterns := []string{
		`^import\s+.*from\s+['"].*['"]`,
		`^const\s+.*=\s+require\(['"].*['"]\)`,
		`^import\s+['"].*['"]`, // import 'style.css'
		`^import\s+\{.*\}\s+from`,
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) isReactComponent(line string) bool {
	patterns := []string{
		`^(export\s+)?(default\s+)?function\s+[A-Z]\w*\s*\(.*\)\s*\{?`,
		`^(export\s+)?(default\s+)?const\s+[A-Z]\w*\s*=\s*\(.*\)\s*=>\s*\{?`,
		`^(export\s+)?(default\s+)?class\s+[A-Z]\w*\s+extends\s+(React\.)?Component`,
		`^(export\s+)?(default\s+)?React\.forwardRef`,
		`^(export\s+)?(default\s+)?React\.memo`,
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) isVueComponent(line string) bool {
	patterns := []string{
		`^(export\s+)?(default\s+)?\{.*name\s*:`,
		`^<template>`,
		`^<script.*setup.*>`,
		`defineComponent\s*\(`,
		`Vue\.component\s*\(`,
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) isJSFunctionDefinition(line string) bool {
	patterns := []string{
		`^(export\s+)?(async\s+)?function\s+\w+\s*\(.*\)\s*\{?`,
		`^(export\s+)?(const|let|var)\s+\w+\s*=\s*(async\s+)?\(.*\)\s*=>\s*\{?`,
		`^\s*\w+\s*:\s*(async\s+)?function\s*\(.*\)\s*\{?`,
		`^\s*async\s+\w+\s*\(.*\)\s*\{?`,
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) isReactHook(line string) bool {
	patterns := []string{
		`^(export\s+)?(const|let)\s+use[A-Z]\w*\s*=`,
		`^function\s+use[A-Z]\w*\s*\(`,
		`useState\s*\(`,
		`useEffect\s*\(`,
		`useCallback\s*\(`,
		`useMemo\s*\(`,
		`useContext\s*\(`,
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, line); matched {
			return true
		}
	}
	return false
}

func (st *SmartTruncator) findVueComponentEnd(lines []string, start int) int {
	// Vue组件可能是对象形式或者SFC形式
	line := strings.TrimSpace(lines[start])

	if strings.HasPrefix(line, "<template>") {
		// Vue SFC 文件，找到 </script> 或文件结束
		for i := start + 1; i < len(lines); i++ {
			if strings.Contains(lines[i], "</script>") {
				return i + 1
			}
		}
		return len(lines)
	}

	// 普通对象形式的组件
	return st.findJSBlockEnd(lines, start)
}

// 辅助函数：找到Java代码块的结束位置
func (st *SmartTruncator) findJavaBlockEnd(lines []string, start int) int {
	braceCount := 0
	found := false

	for i := start; i < len(lines); i++ {
		line := lines[i]
		for _, char := range line {
			if char == '{' {
				braceCount++
				found = true
			} else if char == '}' {
				braceCount--
				if found && braceCount == 0 {
					return i + 1
				}
			}
		}
	}

	return len(lines)
}

// 辅助函数：找到JavaScript代码块的结束位置
func (st *SmartTruncator) findJSBlockEnd(lines []string, start int) int {
	line := strings.TrimSpace(lines[start])

	// 箭头函数可能没有大括号
	if strings.Contains(line, "=>") && !strings.Contains(line, "{") {
		// 单行箭头函数
		return start + 1
	}

	// 有大括号的情况
	braceCount := 0
	found := false

	for i := start; i < len(lines); i++ {
		currentLine := lines[i]
		for _, char := range currentLine {
			if char == '{' {
				braceCount++
				found = true
			} else if char == '}' {
				braceCount--
				if found && braceCount == 0 {
					return i + 1
				}
			}
		}
	}

	return len(lines)
}
