package service

import (
	"fmt"
	"strings"

	"ai-codereview-service/pkg/logger"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/golang"
)

// GoTreeSitterAnalyzer Go专用Tree-sitter分析器
type GoTreeSitterAnalyzer struct {
	*BaseTreeSitterAnalyzer
}

// NewGoTreeSitterAnalyzer 创建Go Tree-sitter分析器
func NewGoTreeSitterAnalyzer() *GoTreeSitterAnalyzer {
	goLanguage := golang.GetLanguage()
	base := NewBaseTreeSitterAnalyzer(goLanguage)

	return &GoTreeSitterAnalyzer{
		BaseTreeSitterAnalyzer: base,
	}
}

// ExtractMethodCalls 提取Go方法调用
func (analyzer *GoTreeSitterAnalyzer) ExtractMethodCalls(tree *sitter.Tree) ([]MethodCall, error) {
	// Go方法调用查询
	query := `
	(call_expression
	  function: (selector_expression
	    operand: (identifier) @object
	    field: (field_identifier) @method)) @call
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query method calls: %w", err)
	}

	var methodCalls []MethodCall
	for _, node := range nodes {
		methodCall := analyzer.parseGoMethodCall(node)
		if methodCall != nil {
			methodCalls = append(methodCalls, *methodCall)
		}
	}

	return methodCalls, nil
}

// ExtractFunctions 提取Go函数信息
func (analyzer *GoTreeSitterAnalyzer) ExtractFunctions(tree *sitter.Tree) ([]MethodInfo, error) {
	// Go函数声明查询
	query := `
	(function_declaration
	  name: (identifier) @func_name
	  parameters: (parameter_list)? @params
	  body: (block)? @body) @function
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query functions: %w", err)
	}

	var functions []MethodInfo
	for _, node := range nodes {
		functionInfo := analyzer.parseGoFunctionInfo(node)
		if functionInfo != nil {
			functions = append(functions, *functionInfo)
		}
	}

	return functions, nil
}

// ExtractMethods 提取Go方法信息
func (analyzer *GoTreeSitterAnalyzer) ExtractMethods(tree *sitter.Tree) ([]MethodInfo, error) {
	// Go方法声明查询
	query := `
	(method_declaration
	  receiver: (parameter_list) @receiver
	  name: (identifier) @method_name
	  parameters: (parameter_list)? @params
	  body: (block)? @body) @method
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query methods: %w", err)
	}

	var methods []MethodInfo
	for _, node := range nodes {
		methodInfo := analyzer.parseGoMethodInfo(node)
		if methodInfo != nil {
			methods = append(methods, *methodInfo)
		}
	}

	return methods, nil
}

// ExtractStructs 提取Go结构体信息
func (analyzer *GoTreeSitterAnalyzer) ExtractStructs(tree *sitter.Tree) ([]ClassInfo, error) {
	// Go结构体声明查询
	query := `
	(type_declaration
	  (type_spec
	    name: (type_identifier) @struct_name
	    type: (struct_type))) @struct
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query structs: %w", err)
	}

	var structs []ClassInfo
	for _, node := range nodes {
		structInfo := analyzer.parseGoStructInfo(node)
		if structInfo != nil {
			structs = append(structs, *structInfo)
		}
	}

	return structs, nil
}

// ExtractImports 提取Go导入信息
func (analyzer *GoTreeSitterAnalyzer) ExtractImports(tree *sitter.Tree) ([]ImportInfo, error) {
	// Go导入声明查询
	query := `
	(import_declaration
	  (import_spec
	    path: (interpreted_string_literal) @import_path)) @import
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query imports: %w", err)
	}

	var imports []ImportInfo
	for _, node := range nodes {
		importInfo := analyzer.parseGoImportInfo(node)
		if importInfo != nil {
			imports = append(imports, *importInfo)
		}
	}

	return imports, nil
}

// DetectGoroutineLeaks 检测Goroutine泄漏风险
func (analyzer *GoTreeSitterAnalyzer) DetectGoroutineLeaks(tree *sitter.Tree) ([]string, error) {
	// 检测无限循环的goroutine
	query := `
	(go_statement
	  (call_expression
	    function: (func_literal
	      body: (block
	        (for_statement
	          condition: (true))) @infinite_loop))) @goroutine
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect goroutine leaks: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		position := analyzer.GetPosition(node)
		issues = append(issues, fmt.Sprintf("疑似Goroutine泄漏: 第%d行，无限循环的goroutine未正确关闭",
			position.StartRow+1))
	}

	return issues, nil
}

// DetectRaceConditions 检测数据竞争风险
func (analyzer *GoTreeSitterAnalyzer) DetectRaceConditions(tree *sitter.Tree) ([]string, error) {
	return analyzer.DetectRaceConditionsWithConfig(tree, 1000, 50)
}

// DetectRaceConditionsWithConfig 带配置的数据竞争检测
func (analyzer *GoTreeSitterAnalyzer) DetectRaceConditionsWithConfig(tree *sitter.Tree, maxGlobalVars, maxDepth int) ([]string, error) {
	// 检测并发访问共享变量
	rootNode := tree.RootNode()
	var issues []string

	// 查找全局变量，限制map大小以防止内存泄漏
	globalVars := make(map[string]bool)

	// 使用改进的深度控制方法查找全局变量
	analyzer.walkTreeWithDepthLimit(rootNode, maxDepth, func(node *sitter.Node, depth int) bool {
		if node.Type() == "var_declaration" {
			// 检查是否为全局变量
			if analyzer.isGlobalScope(node) && len(globalVars) < maxGlobalVars {
				varNames := analyzer.extractVariableNames(node)
				for _, varName := range varNames {
					if len(globalVars) >= maxGlobalVars {
						break
					}
					globalVars[varName] = true
				}
			}
		}
		return true
	})

	// 检查goroutine中对全局变量的访问
	analyzer.walkTreeWithDepthLimit(rootNode, maxDepth, func(node *sitter.Node, depth int) bool {
		if node.Type() == "go_statement" {
			// 检查goroutine内部的变量访问
			analyzer.walkTreeWithDepthLimit(node, maxDepth, func(inner *sitter.Node, innerDepth int) bool {
				if inner.Type() == "identifier" {
					varName := analyzer.GetNodeText(inner)
					if globalVars[varName] {
						position := analyzer.GetPosition(inner)
						issues = append(issues, fmt.Sprintf("疑似数据竞争: 第%d行，goroutine中访问全局变量 %s",
							position.StartRow+1, varName))
					}
				}
				return true
			})
		}
		return true
	})

	return issues, nil
}

// DetectMemoryLeaks 检测内存泄漏风险
func (analyzer *GoTreeSitterAnalyzer) DetectMemoryLeaks(tree *sitter.Tree) ([]string, error) {
	// 检测未关闭的资源
	query := `
	(assignment_statement
	  left: (expression_list
	    (identifier) @var)
	  right: (expression_list
	    (call_expression
	      function: (selector_expression
	        field: (field_identifier) @method)
	      (#match? @method "^(Open|Create|NewReader|NewWriter).*")))) @resource_open
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect memory leaks: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		position := analyzer.GetPosition(node)
		varName := analyzer.GetNodeText(analyzer.FindChildByType(node, "identifier"))

		// 检查是否有对应的Close调用
		if !analyzer.hasCloseCall(node, varName) {
			issues = append(issues, fmt.Sprintf("疑似内存泄漏: 第%d行，资源 %s 可能未正确关闭",
				position.StartRow+1, varName))
		}
	}

	return issues, nil
}

// DetectErrorHandling 检测错误处理问题
func (analyzer *GoTreeSitterAnalyzer) DetectErrorHandling(tree *sitter.Tree) ([]string, error) {
	// 检测未处理的错误
	query := `
	(assignment_statement
	  left: (expression_list
	    (identifier)
	    (identifier) @error_var
	    (#eq? @error_var "err"))
	  right: (expression_list
	    (call_expression))) @error_assignment
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect error handling: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		// 检查后续是否有错误处理
		if !analyzer.hasErrorHandling(node) {
			position := analyzer.GetPosition(node)
			issues = append(issues, fmt.Sprintf("错误处理缺失: 第%d行，返回的错误未进行检查",
				position.StartRow+1))
		}
	}

	return issues, nil
}

// 辅助方法
func (analyzer *GoTreeSitterAnalyzer) parseGoMethodCall(node *sitter.Node) *MethodCall {
	if node == nil {
		return nil
	}

	methodNode := analyzer.FindChildByType(node, "field_identifier")
	objectNode := analyzer.FindChildByType(node, "identifier")

	if methodNode == nil || objectNode == nil {
		return nil
	}

	methodName := analyzer.GetNodeText(methodNode)
	objectName := analyzer.GetNodeText(objectNode)
	position := analyzer.GetPosition(node)

	return &MethodCall{
		Object:     objectName,
		Method:     methodName,
		LineNumber: position.StartRow + 1,
		Position:   position,
	}
}

func (analyzer *GoTreeSitterAnalyzer) parseGoFunctionInfo(node *sitter.Node) *MethodInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "identifier")
	if nameNode == nil {
		return nil
	}

	functionName := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	// 解析参数
	var parameters []string
	paramsNode := analyzer.FindChildByType(node, "parameter_list")
	if paramsNode != nil {
		paramText := analyzer.GetNodeText(paramsNode)
		parameters = append(parameters, paramText)
	}

	// 获取函数体
	bodyNode := analyzer.FindChildByType(node, "block")
	bodyText := ""
	if bodyNode != nil {
		bodyText = analyzer.GetNodeText(bodyNode)
	}

	return &MethodInfo{
		Name:       functionName,
		Parameters: parameters,
		Position:   position,
		Body:       bodyText,
	}
}

func (analyzer *GoTreeSitterAnalyzer) parseGoMethodInfo(node *sitter.Node) *MethodInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "identifier")
	if nameNode == nil {
		return nil
	}

	methodName := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	// 解析接收者
	receiverNode := analyzer.FindChildByType(node, "parameter_list")
	receiverText := ""
	if receiverNode != nil {
		receiverText = analyzer.GetNodeText(receiverNode)
	}

	// 解析参数
	var parameters []string
	if receiverText != "" {
		parameters = append(parameters, receiverText)
	}

	// 获取方法体
	bodyNode := analyzer.FindChildByType(node, "block")
	bodyText := ""
	if bodyNode != nil {
		bodyText = analyzer.GetNodeText(bodyNode)
	}

	return &MethodInfo{
		Name:       methodName,
		Parameters: parameters,
		Position:   position,
		Body:       bodyText,
	}
}

func (analyzer *GoTreeSitterAnalyzer) parseGoStructInfo(node *sitter.Node) *ClassInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "type_identifier")
	if nameNode == nil {
		return nil
	}

	structName := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	return &ClassInfo{
		Name:     structName,
		Position: position,
	}
}

func (analyzer *GoTreeSitterAnalyzer) parseGoImportInfo(node *sitter.Node) *ImportInfo {
	if node == nil {
		return nil
	}

	pathNode := analyzer.FindChildByType(node, "interpreted_string_literal")
	if pathNode == nil {
		return nil
	}

	importPath := strings.Trim(analyzer.GetNodeText(pathNode), `"`)
	position := analyzer.GetPosition(node)

	return &ImportInfo{
		Package:  importPath,
		IsStatic: false,
		IsWild:   false,
		Position: position,
	}
}

func (analyzer *GoTreeSitterAnalyzer) isGlobalScope(node *sitter.Node) bool {
	// 检查节点是否在全局作用域
	parent := node.Parent()
	for parent != nil {
		if parent.Type() == "function_declaration" || parent.Type() == "method_declaration" {
			return false
		}
		parent = parent.Parent()
	}
	return true
}

func (analyzer *GoTreeSitterAnalyzer) extractVariableNames(node *sitter.Node) []string {
	var names []string
	analyzer.WalkTree(node, func(n *sitter.Node) bool {
		if n.Type() == "identifier" {
			names = append(names, analyzer.GetNodeText(n))
		}
		return true
	})
	return names
}

func (analyzer *GoTreeSitterAnalyzer) hasCloseCall(node *sitter.Node, varName string) bool {
	// TODO: 需要实现更复杂的作用域分析来准确检测Close调用
	// 目前实现基础版本：检查当前作用域内是否有Close方法调用

	if node == nil || varName == "" {
		return false
	}

	// 获取当前节点的父函数或方法
	current := node.Parent()
	for current != nil {
		if current.Type() == "function_declaration" || current.Type() == "method_declaration" {
			break
		}
		current = current.Parent()
	}

	if current == nil {
		return false
	}

	// 简单检查：在当前函数/方法体中查找包含变量名和Close的调用
	bodyText := analyzer.GetNodeText(current)
	return strings.Contains(bodyText, varName+".Close()") ||
		strings.Contains(bodyText, "defer "+varName+".Close()")
}

func (analyzer *GoTreeSitterAnalyzer) hasErrorHandling(node *sitter.Node) bool {
	// TODO: 需要实现更精确的错误处理检测
	// 目前实现基础版本：检查后续是否有if err != nil语句

	if node == nil {
		return false
	}

	// 获取当前节点的父函数或方法
	current := node.Parent()
	for current != nil {
		if current.Type() == "function_declaration" || current.Type() == "method_declaration" {
			break
		}
		current = current.Parent()
	}

	if current == nil {
		return false
	}

	// 简单检查：在当前函数/方法体中查找错误处理模式
	bodyText := analyzer.GetNodeText(current)
	return strings.Contains(bodyText, "if err != nil") ||
		strings.Contains(bodyText, "if error != nil") ||
		strings.Contains(bodyText, "return") && strings.Contains(bodyText, "err")
}

// walkTreeWithDepthLimit 带深度限制的树遍历方法
func (analyzer *GoTreeSitterAnalyzer) walkTreeWithDepthLimit(node *sitter.Node, maxDepth int, visitor func(*sitter.Node, int) bool) {
	var walk func(*sitter.Node, int)
	walk = func(n *sitter.Node, depth int) {
		if depth > maxDepth {
			return
		}
		if !visitor(n, depth) {
			return
		}
		for i := 0; i < int(n.ChildCount()); i++ {
			walk(n.Child(i), depth+1)
		}
	}
	walk(node, 0)
}

// AnalyzeGoCode 综合分析Go代码
func (analyzer *GoTreeSitterAnalyzer) AnalyzeGoCode(code string) (*GoAnalysisResult, error) {
	tree, err := analyzer.ParseCode(code)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Go code: %w", err)
	}
	defer tree.Close() // 简化defer调用

	analyzer.LogParsingStats(tree, "Go")

	// 提取各种信息
	methodCalls, _ := analyzer.ExtractMethodCalls(tree)
	functions, _ := analyzer.ExtractFunctions(tree)
	methods, _ := analyzer.ExtractMethods(tree)
	structs, _ := analyzer.ExtractStructs(tree)
	imports, _ := analyzer.ExtractImports(tree)

	// 检测问题
	goroutineLeaks, _ := analyzer.DetectGoroutineLeaks(tree)
	raceConditions, _ := analyzer.DetectRaceConditions(tree)
	memoryLeaks, _ := analyzer.DetectMemoryLeaks(tree)
	errorHandling, _ := analyzer.DetectErrorHandling(tree)

	result := &GoAnalysisResult{
		MethodCalls:    methodCalls,
		Functions:      functions,
		Methods:        methods,
		Structs:        structs,
		Imports:        imports,
		GoroutineLeaks: goroutineLeaks,
		RaceConditions: raceConditions,
		MemoryLeaks:    memoryLeaks,
		ErrorHandling:  errorHandling,
	}

	logger.Infof("Go代码分析完成: 方法调用%d个, 函数%d个, 方法%d个, 结构体%d个, 导入%d个",
		len(methodCalls), len(functions), len(methods), len(structs), len(imports))

	return result, nil
}

// GoAnalysisResult Go代码分析结果
type GoAnalysisResult struct {
	MethodCalls    []MethodCall `json:"method_calls"`
	Functions      []MethodInfo `json:"functions"`
	Methods        []MethodInfo `json:"methods"`
	Structs        []ClassInfo  `json:"structs"`
	Imports        []ImportInfo `json:"imports"`
	GoroutineLeaks []string     `json:"goroutine_leaks"`
	RaceConditions []string     `json:"race_conditions"`
	MemoryLeaks    []string     `json:"memory_leaks"`
	ErrorHandling  []string     `json:"error_handling"`
}
