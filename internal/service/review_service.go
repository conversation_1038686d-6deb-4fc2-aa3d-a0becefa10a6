package service

import (
	"fmt"
	"os"
	"strings"
	"time"

	"ai-codereview-service/internal/config"
	"ai-codereview-service/internal/gitlab"
	"ai-codereview-service/internal/llm"
	"ai-codereview-service/internal/notification"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

type ReviewService struct {
	config              *config.Config
	repo                *repository.DBRepository
	llmClient           *llm.Client
	notifier            *notification.DingTalkNotifier
	unifiedReviewEngine *UnifiedReviewEngine // 统一审查引擎
}

type PromptTemplate struct {
	SystemPrompt string `yaml:"system_prompt"`
	UserPrompt   string `yaml:"user_prompt"`
}

// safeSubstring 安全的字符串截取函数
func safeSubstring(s string, length int) string {
	if len(s) <= length {
		return s
	}
	return s[:length]
}

// buildDingTalkWebhookURL 根据token构建完整的钉钉webhook URL
func buildDingTalkWebhookURL(token string) string {
	return fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=%s", token)
}

// getDingTalkNotifiers 根据项目名称、分支和事件类型获取钉钉通知器
func (s *ReviewService) getDingTalkNotifiers(projectName, branch, eventType string) []*notification.DingTalkNotifier {
	var notifiers []*notification.DingTalkNotifier

	// 使用异步方式查询数据库，避免阻塞主流程
	configsChan := make(chan []repository.DingTalkNotificationConfig, 1)
	errChan := make(chan error, 1)

	go func() {
		configs, err := s.repo.GetMatchingDingTalkConfigs(projectName, branch, eventType)
		if err != nil {
			errChan <- err
			return
		}
		configsChan <- configs
	}()

	// 设置超时，避免数据库查询影响主流程
	select {
	case configs := <-configsChan:
		// 如果找到匹配的配置，为每个配置创建通知器
		if len(configs) > 0 {
			logger.Infof("找到 %d 个匹配的钉钉配置: 项目=%s, 分支=%s, 事件=%s",
				len(configs), projectName, branch, eventType)
			for _, config := range configs {
				webhookURL := buildDingTalkWebhookURL(config.Token)
				notifier := notification.NewDingTalkNotifier(webhookURL, true)
				notifiers = append(notifiers, notifier)
			}
			return notifiers
		}
	case err := <-errChan:
		logger.Errorf("Failed to get dingtalk configs from database: %v", err)
	case <-time.After(500 * time.Millisecond): // 500ms超时
		logger.Warnf("Database query timeout for dingtalk configs, using default config")
	}

	// 如果没有找到匹配的配置或查询失败，使用默认配置
	logger.Debugf("使用默认钉钉配置: 项目=%s, 分支=%s, 事件=%s",
		projectName, branch, eventType)
	if s.config.DingTalk.Enabled && s.config.DingTalk.WebhookURL != "" {
		notifiers = append(notifiers, s.notifier)
	}

	return notifiers
}

// sendNotificationAsync 异步发送钉钉通知
func (s *ReviewService) sendNotificationAsync(projectName, branch, eventType, title, content string) {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("Panic in async notification: %v", r)
		}
	}()

	// 使用动态钉钉配置发送通知
	notifiers := s.getDingTalkNotifiers(projectName, branch, eventType)
	success := false
	for _, notifier := range notifiers {
		if err := notifier.SendMarkdown(title, content, false); err != nil {
			logger.Errorf("Failed to send notification: %v", err)
		} else {
			logger.Infof("钉钉通知发送成功: %s (分支: %s, 事件: %s)", projectName, branch, eventType)
			success = true
		}
	}

	if !success && len(notifiers) == 0 {
		logger.Warnf("未找到匹配的钉钉配置，项目: %s, 分支: %s, 事件: %s", projectName, branch, eventType)
	}
}

// isAutoReviewEnabled 智能判断是否启用自动审核
// 优先级：MySQL项目配置 > config.yaml全局配置
func (s *ReviewService) isAutoReviewEnabled(projectName string) bool {
	// 首先检查该项目是否在数据库中有钉钉配置
	hasConfig, err := s.repo.HasDingTalkConfigForProject(projectName)
	if err != nil {
		logger.Warnf("检查项目钉钉配置失败: %v，使用默认配置", err)
		// 如果查询失败，回退到config.yaml配置
		return s.config.Review.AutoEnabled
	}

	if hasConfig {
		// 如果项目在数据库中有配置，则启用自动审核
		logger.Infof("项目 %s 在数据库中有钉钉配置，启用自动审核", projectName)
		return true
	}

	// 如果项目没有在数据库中配置，使用config.yaml的默认设置
	enabled := s.config.Review.AutoEnabled
	logger.Debugf("项目 %s 使用全局配置，自动审核: %v", projectName, enabled)
	return enabled
}

func NewReviewService(
	cfg *config.Config,
	repo *repository.DBRepository,
	llmClient *llm.Client,
	notifier *notification.DingTalkNotifier,
) *ReviewService {
	return &ReviewService{
		config:              cfg,
		repo:                repo,
		llmClient:           llmClient,
		notifier:            notifier,
		unifiedReviewEngine: NewUnifiedReviewEngine(cfg, llmClient), // 统一审查引擎
	}
}

// extractGitLabInfo 从配置和webhook数据中提取GitLab信息
func (s *ReviewService) extractGitLabInfo(webhook *repository.GitLabWebhook) (string, string, string, error) {
	// 获取GitLab URL和Token
	gitlabURL := os.Getenv("GITLAB_URL")
	if gitlabURL == "" && s.config.GitLab.URL != "" {
		gitlabURL = s.config.GitLab.URL
	}
	if gitlabURL == "" {
		return "", "", "", fmt.Errorf("missing GitLab URL")
	}

	gitlabToken := os.Getenv("GITLAB_ACCESS_TOKEN")
	if gitlabToken == "" && s.config.GitLab.Token != "" {
		gitlabToken = s.config.GitLab.Token
	}
	if gitlabToken == "" {
		return "", "", "", fmt.Errorf("missing GitLab access token")
	}

	// 从webhook数据中获取project_id
	var projectIDInt int
	if webhook.Project.ID != 0 {
		projectIDInt = webhook.Project.ID
	} else if webhook.ProjectID != 0 {
		projectIDInt = webhook.ProjectID
	} else if webhook.ObjectAttributes.TargetProjectID != 0 {
		projectIDInt = webhook.ObjectAttributes.TargetProjectID
	}

	var projectID string
	if projectIDInt == 0 {
		// 🔧 新增：当project ID缺失时，尝试通过项目名称查找
		logger.Warnf("Project ID missing in webhook, trying to find by project name: %s", webhook.Project.Name)
		if webhook.Project.Name == "" {
			return "", "", "", fmt.Errorf("missing both project ID and project name in webhook data")
		}

		// 尝试通过项目名称查找project ID
		foundProjectID, err := s.findProjectIDByName(gitlabURL, gitlabToken, webhook.Project.Name)
		if err != nil {
			return "", "", "", fmt.Errorf("failed to find project ID by name '%s': %w", webhook.Project.Name, err)
		}
		projectID = fmt.Sprintf("%d", foundProjectID)
		logger.Infof("✅ Found project ID by name: %s -> %s", webhook.Project.Name, projectID)
	} else {
		projectID = fmt.Sprintf("%d", projectIDInt)
	}

	// 🔧 **智能项目ID验证和修正机制**（如果有commits可以验证的话）
	if len(webhook.Commits) > 0 || webhook.After != "" {
		correctedProjectID, err := s.validateAndCorrectProjectID(gitlabURL, gitlabToken, projectID, webhook)
		if err != nil {
			logger.Warnf("项目ID验证失败，使用原始ID: %v", err)
			// 不返回错误，继续使用原始项目ID
		} else if correctedProjectID != projectID {
			logger.Infof("项目ID已修正: %s -> %s", projectID, correctedProjectID)
			projectID = correctedProjectID
		}
	}

	return gitlabURL, gitlabToken, projectID, nil
}

// validateAndCorrectProjectID 验证并修正项目ID
func (s *ReviewService) validateAndCorrectProjectID(gitlabURL, gitlabToken, originalProjectID string, webhook *repository.GitLabWebhook) (string, error) {
	// 创建临时客户端用于验证
	tempClient := gitlab.NewClient(gitlabURL, gitlabToken, originalProjectID)

	// 获取第一个commit来进行验证
	var testCommitID string
	if len(webhook.Commits) > 0 {
		testCommitID = webhook.Commits[0].ID
	} else if webhook.After != "" && webhook.After != "0000000000000000000000000000000000000000" {
		testCommitID = webhook.After
	} else if webhook.CheckoutSHA != "" {
		testCommitID = webhook.CheckoutSHA
	}

	if testCommitID == "" {
		logger.Debug("没有找到可用于验证的commit ID")
		return originalProjectID, nil
	}

	logger.Infof("验证项目ID %s 中是否存在commit %s", originalProjectID, testCommitID[:8])

	// 1. 验证原始项目ID中是否存在该commit
	_, err := tempClient.GetCommit(testCommitID)
	if err == nil {
		logger.Infof("✅ 项目ID %s 验证通过", originalProjectID)
		return originalProjectID, nil
	}

	if !strings.Contains(err.Error(), "404") {
		// 如果不是404错误，可能是网络问题等，不进行修正
		return originalProjectID, fmt.Errorf("验证项目ID时发生非404错误: %w", err)
	}

	logger.Warnf("❌ 项目ID %s 中未找到commit %s，开始智能修正", originalProjectID, testCommitID[:8])

	// 2. 根据项目名称和commit特征自动查找正确的项目ID
	correctProjectID, err := s.findCorrectProjectIDByCommit(gitlabURL, gitlabToken, testCommitID, webhook.Project.Name)
	if err != nil {
		return originalProjectID, fmt.Errorf("智能修正项目ID失败: %w", err)
	}

	logger.Infof("🔧 智能修正成功: 项目ID %s -> %s", originalProjectID, correctProjectID)
	return correctProjectID, nil
}

// findCorrectProjectIDByCommit 通过commit查找正确的项目ID
func (s *ReviewService) findCorrectProjectIDByCommit(gitlabURL, gitlabToken, commitID, projectName string) (string, error) {
	// 基于项目名称的智能搜索策略
	searchTerms := s.generateProjectSearchTerms(projectName)

	tempClient := gitlab.NewClient(gitlabURL, gitlabToken, "0") // 使用临时项目ID

	for _, searchTerm := range searchTerms {
		logger.Debugf("搜索包含 '%s' 的项目", searchTerm)

		// 使用FindProjectByName来搜索相似项目
		projectID, err := tempClient.FindProjectByName(searchTerm)
		if err != nil {
			logger.Debugf("搜索项目 '%s' 失败: %v", searchTerm, err)
			continue
		}

		// 验证该项目中是否存在目标commit
		testClient := gitlab.NewClient(gitlabURL, gitlabToken, fmt.Sprintf("%d", projectID))
		_, err = testClient.GetCommit(commitID)
		if err == nil {
			logger.Infof("✅ 在项目ID %d 中找到了commit %s", projectID, commitID[:8])
			return fmt.Sprintf("%d", projectID), nil
		}

		logger.Debugf("项目ID %d 中未找到commit %s", projectID, commitID[:8])
	}

	// 如果基于项目名称的搜索失败，尝试常见的项目映射
	knownMappings := s.getKnownProjectMappings()
	for pattern, candidateIDs := range knownMappings {
		if strings.Contains(strings.ToLower(projectName), pattern) {
			for _, candidateID := range candidateIDs {
				logger.Debugf("尝试已知映射项目ID %s", candidateID)
				testClient := gitlab.NewClient(gitlabURL, gitlabToken, candidateID)
				_, err := testClient.GetCommit(commitID)
				if err == nil {
					logger.Infof("✅ 通过已知映射找到正确项目ID %s", candidateID)
					return candidateID, nil
				}
			}
		}
	}

	return "", fmt.Errorf("未找到包含commit %s 的正确项目", commitID[:8])
}

// generateProjectSearchTerms 生成项目搜索关键词
func (s *ReviewService) generateProjectSearchTerms(projectName string) []string {
	var terms []string

	// 1. 原始项目名
	terms = append(terms, projectName)

	// 2. 提取关键词
	name := strings.ToLower(projectName)

	// 提取insight相关关键词
	if strings.Contains(name, "insight") {
		terms = append(terms, "insight", "risk-insight", "insight-service", "insight-portal")
	}

	// 提取recommend相关关键词
	if strings.Contains(name, "recommend") {
		terms = append(terms, "recommend", "yuer-recommend", "recommend-service", "recommend-api")
	}

	// 提取其他常见关键词
	keywords := []string{"service", "api", "web", "portal", "core", "common"}
	for _, keyword := range keywords {
		if strings.Contains(name, keyword) {
			terms = append(terms, keyword)
		}
	}

	// 3. 去除重复项
	uniqueTerms := make([]string, 0)
	seen := make(map[string]bool)
	for _, term := range terms {
		if !seen[term] {
			seen[term] = true
			uniqueTerms = append(uniqueTerms, term)
		}
	}

	return uniqueTerms
}

// getKnownProjectMappings 获取已知的项目映射关系
func (s *ReviewService) getKnownProjectMappings() map[string][]string {
	return map[string][]string{
		"insight":   {"1679", "1680", "9247"}, // risk-insight, insight-portal, Insight等
		"recommend": {"8178", "8179", "8180"}, // 各种recommend相关项目
		"risk":      {"1679", "1680"},         // 风控相关项目
		"yuer":      {"8178"},                 // yuer相关项目
	}
}

// createGitLabClient 为特定webhook创建GitLab客户端
func (s *ReviewService) createGitLabClient(webhook *repository.GitLabWebhook) (*gitlab.Client, error) {
	gitlabURL, gitlabToken, projectID, err := s.extractGitLabInfo(webhook)
	if err != nil {
		return nil, err
	}

	return gitlab.NewClient(gitlabURL, gitlabToken, projectID), nil
}

// HandlePushEvent 处理Push事件
func (s *ReviewService) HandlePushEvent(webhook *repository.GitLabWebhook) error {
	// 智能检查自动审查是否开启（优先级：MySQL项目配置 > config.yaml全局配置）
	if !s.isAutoReviewEnabled(webhook.Project.Name) {
		logger.Infof("Auto review is disabled for project %s, skipping push event", webhook.Project.Name)
		return nil
	}
	logger.Infof("Processing push event for project: %s, branch: %s", webhook.Project.Name, s.extractBranchName(webhook.Ref))

	// 创建GitLab客户端
	gitlabClient, err := s.createGitLabClient(webhook)
	if err != nil {
		logger.Errorf("Failed to create GitLab client: %v", err)
		return err
	}

	// 检查分支过滤
	branchName := s.extractBranchName(webhook.Ref)
	if !s.isTargetBranch(branchName) {
		logger.Infof("Push to branch %s ignored, not in target branches", branchName)
		return nil
	}

	// 检查是否已经审查过
	if s.isPushAlreadyReviewed(webhook) {
		logger.Infof("Push already reviewed, skipping: %s..%s", webhook.Before[:8], webhook.After[:8])
		return nil
	}

	var reviewResult string
	if s.config.Review.Enabled {
		// 获取代码变更
		changes, err := gitlabClient.CompareCommits(webhook.Before, webhook.After)
		if err != nil {
			logger.Errorf("Failed to get changes: %v", err)
			return err
		}

		// 过滤变更
		filteredChanges := gitlab.FilterChanges(changes)
		if len(filteredChanges) == 0 {
			logger.Info("No code changes detected")
			reviewResult = "没有检测到文件变更"
		} else {
			// 进行代码审查
			logger.Infof("开始审查代码，共 %d 个文件变更", len(filteredChanges))
			reviewResult, err = s.reviewCode(filteredChanges, webhook.Commits, gitlabClient, webhook.After, branchName)
			if err != nil {
				logger.Errorf("Failed to review code: %v", err)
				return err
			}
			logger.Infof("代码审查完成，结果长度: %d 字符", len(reviewResult))

			// 添加GitLab评论
			if s.config.GitLab.CommentEnabled && len(webhook.Commits) > 0 {
				lastCommitID := webhook.Commits[len(webhook.Commits)-1].ID
				comment := fmt.Sprintf("Auto Review Result:\n%s", reviewResult)
				logger.Infof("正在向 GitLab 提交 %s 添加评论", lastCommitID[:8])
				if err := gitlabClient.AddCommitComment(lastCommitID, comment); err != nil {
					logger.Errorf("Failed to add commit comment: %v", err)
				} else {
					logger.Info("GitLab 评论添加成功")
				}
			}
		}
	}

	// 保存审查记录
	pushLog := &repository.PushReviewLog{
		ProjectName:  webhook.Project.Name,
		Author:       webhook.UserUsername,
		Branch:       branchName,
		BeforeCommit: webhook.Before,
		AfterCommit:  webhook.After,
		ReviewResult: reviewResult,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	logger.Infof("保存 Push 审查记录到数据库: %s/%s", webhook.Project.Name, branchName)
	if err := s.repo.InsertPushReviewLog(pushLog); err != nil {
		logger.Errorf("Failed to save push review log: %v", err)
	} else {
		logger.Info("Push 审查记录保存成功")
	}

	// 发送通知
	logger.Info("发送钉钉通知...")
	s.sendPushNotification(webhook, reviewResult)

	return nil
}

// HandleMergeRequestEvent 处理MR事件
func (s *ReviewService) HandleMergeRequestEvent(webhook *repository.GitLabWebhook) error {
	// 智能检查自动审查是否开启（优先级：MySQL项目配置 > config.yaml全局配置）
	if !s.isAutoReviewEnabled(webhook.Project.Name) {
		logger.Infof("Auto review is disabled for project %s, skipping merge request event", webhook.Project.Name)
		return nil
	}

	// 只处理open和update事件
	if webhook.ObjectAttributes.Action != "open" && webhook.ObjectAttributes.Action != "reopen" && webhook.ObjectAttributes.Action != "update" {
		logger.Infof("MR action '%s' ignored", webhook.ObjectAttributes.Action)
		return nil
	}

	logger.Infof("Processing MR event for project: %s, action: %s", webhook.Project.Name, webhook.ObjectAttributes.Action)

	// 创建GitLab客户端
	gitlabClient, err := s.createGitLabClient(webhook)
	if err != nil {
		logger.Errorf("Failed to create GitLab client: %v", err)
		return err
	}

	// 检查目标分支过滤
	if !s.isTargetBranch(webhook.ObjectAttributes.TargetBranch) {
		logger.Infof("MR to branch %s ignored, not in target branches", webhook.ObjectAttributes.TargetBranch)
		return nil
	}

	// 获取MR变更
	changes, err := gitlabClient.GetMergeRequestChanges(webhook.ObjectAttributes.IID)
	if err != nil {
		logger.Errorf("Failed to get MR changes: %v", err)
		return err
	}

	// 过滤变更
	filteredChanges := gitlab.FilterChanges(changes)
	if len(filteredChanges) == 0 {
		logger.Info("No code changes detected in MR")
		return nil
	}

	// 获取MR提交
	commits, err := gitlabClient.GetMergeRequestCommits(webhook.ObjectAttributes.IID)
	if err != nil {
		logger.Errorf("Failed to get MR commits: %v", err)
		return err
	}

	// 进行代码审查
	logger.Infof("开始审查 MR 代码，共 %d 个文件变更", len(filteredChanges))
	reviewResult, err := s.reviewCodeFromMR(filteredChanges, commits, gitlabClient, webhook.ObjectAttributes.SourceBranch, webhook.ObjectAttributes.SourceBranch)
	if err != nil {
		logger.Errorf("Failed to review MR code: %v", err)
		return err
	}
	logger.Infof("MR 代码审查完成，结果长度: %d 字符", len(reviewResult))

	// 添加MR评论
	if s.config.GitLab.CommentEnabled {
		comment := fmt.Sprintf("Auto Review Result:\n%s", reviewResult)
		logger.Infof("正在向 MR #%d 添加审查评论", webhook.ObjectAttributes.IID)
		if err := gitlabClient.AddMergeRequestNote(webhook.ObjectAttributes.IID, comment); err != nil {
			logger.Errorf("Failed to add MR comment: %v", err)
		} else {
			logger.Info("MR 审查评论添加成功")
		}
	}

	// 保存审查记录
	mrLog := &repository.MergeRequestReviewLog{
		ProjectName:     webhook.Project.Name,
		Author:          webhook.User.Name,
		SourceBranch:    webhook.ObjectAttributes.SourceBranch,
		TargetBranch:    webhook.ObjectAttributes.TargetBranch,
		MergeRequestIID: webhook.ObjectAttributes.IID,
		ReviewResult:    reviewResult,
		URL:             webhook.ObjectAttributes.URL,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	logger.Infof("保存 MR 审查记录到数据库: %s#%d", webhook.Project.Name, webhook.ObjectAttributes.IID)
	if err := s.repo.InsertMRReviewLog(mrLog); err != nil {
		logger.Errorf("Failed to save MR review log: %v", err)
	} else {
		logger.Info("MR 审查记录保存成功")
	}

	// 发送通知
	s.sendMRNotification(webhook, reviewResult)

	return nil
}

// reviewCode 进行代码审查（用于Push事件）
func (s *ReviewService) reviewCode(changes []repository.GitLabChange, commits []repository.Commit, gitlabClient *gitlab.Client, ref string, branchName string) (string, error) {
	// 创建统一审查引擎
	if s.unifiedReviewEngine == nil {
		s.unifiedReviewEngine = NewUnifiedReviewEngine(s.config, s.llmClient)
	}

	// 转换提交信息
	var commitInfos []repository.GitLabCommitInfo
	for _, commit := range commits {
		commitInfos = append(commitInfos, repository.GitLabCommitInfo{
			ID:      commit.ID,
			Title:   commit.Message,
			Message: commit.Message,
		})
	}

	// 构建审查请求
	request := &ReviewRequest{
		Changes:      changes,
		Commits:      commitInfos,
		GitlabClient: gitlabClient,
		Ref:          ref,
		BranchName:   branchName,
		ReviewType:   ReviewTypeEnhanced, // 默认使用增强审查
	}

	// 执行统一审查
	logger.Infof("调用LLM进行代码审查，使用模型: %s", s.config.LLM.Model)
	result, err := s.unifiedReviewEngine.ReviewCode(request)
	if err != nil {
		return "", fmt.Errorf("统一审查失败: %w", err)
	}

	return result.Result, nil
}

// buildCommitsText 构建提交信息文本
func (s *ReviewService) buildCommitsText(commits []repository.Commit) string {
	var commitMessages []string
	for _, commit := range commits {
		commitMessages = append(commitMessages, strings.TrimSpace(commit.Message))
	}
	return strings.Join(commitMessages, "; ")
}

// reviewCodeFromMR 进行代码审查（用于MR事件）
func (s *ReviewService) reviewCodeFromMR(changes []repository.GitLabChange, commits []repository.GitLabCommitInfo, gitlabClient *gitlab.Client, ref string, branchName string) (string, error) {
	// 创建统一审查引擎
	if s.unifiedReviewEngine == nil {
		s.unifiedReviewEngine = NewUnifiedReviewEngine(s.config, s.llmClient)
	}

	// 构建审查请求
	request := &ReviewRequest{
		Changes:      changes,
		Commits:      commits,
		GitlabClient: gitlabClient,
		Ref:          ref,
		BranchName:   branchName,
		ReviewType:   ReviewTypeEnhanced,
	}

	// 执行统一审查
	logger.Infof("调用LLM进行代码审查，使用模型: %s", s.config.LLM.Model)
	result, err := s.unifiedReviewEngine.ReviewCode(request)
	if err != nil {
		return "", fmt.Errorf("代码审查失败: %w", err)
	}

	return result.Result, nil
}

func (s *ReviewService) buildChangesText(changes []repository.GitLabChange) string {
	var builder strings.Builder
	for _, change := range changes {
		builder.WriteString(fmt.Sprintf("File: %s\n", change.NewPath))
		builder.WriteString(change.Diff)
		builder.WriteString("\n\n")
	}
	return builder.String()
}

func (s *ReviewService) extractBranchName(ref string) string {
	return strings.TrimPrefix(ref, "refs/heads/")
}

func (s *ReviewService) isTargetBranch(branch string) bool {
	for _, target := range s.config.GitLab.TargetBranches {
		if branch == target {
			return true
		}
	}
	return false
}

func (s *ReviewService) isPushAlreadyReviewed(webhook *repository.GitLabWebhook) bool {
	if webhook.Before == "" || webhook.After == "" {
		return false
	}

	reviewed, err := s.repo.IsPushAlreadyReviewed(
		webhook.Project.Name,
		s.extractBranchName(webhook.Ref),
		webhook.Before,
		webhook.After,
	)

	if err != nil {
		logger.Errorf("Failed to check if push already reviewed: %v", err)
		return false
	}

	return reviewed
}

func (s *ReviewService) sendPushNotification(webhook *repository.GitLabWebhook, reviewResult string) {
	var content strings.Builder
	content.WriteString(fmt.Sprintf("### 🚀 %s: Push\n\n", webhook.Project.Name))
	content.WriteString("#### 提交记录:\n")
	content.WriteString(fmt.Sprintf("- **操作账号**: %s\n", webhook.UserUsername))

	// 只显示最新的3个提交
	commits := webhook.Commits
	if len(commits) > 3 {
		content.WriteString(fmt.Sprintf("*（显示最新3个提交，共%d个提交）*\n\n", len(commits)))
		commits = commits[len(commits)-3:]
	}

	for _, commit := range commits {
		formattedTime := notification.FormatChinaTime(commit.Timestamp)
		content.WriteString(fmt.Sprintf("- **提交信息**: %s\n", strings.TrimSpace(commit.Message)))
		content.WriteString(fmt.Sprintf("- **提交者**: %s\n", commit.Author.Name))
		content.WriteString(fmt.Sprintf("- **时间**: %s\n", formattedTime))
		content.WriteString(fmt.Sprintf("- [查看提交详情](%s)\n\n", commit.URL))
	}

	if reviewResult != "" {
		content.WriteString(fmt.Sprintf("#### AI Review 结果:\n%s\n\n", reviewResult))
	}

	title := fmt.Sprintf("%s Push Event", webhook.Project.Name)

	// 异步发送通知，避免影响主流程
	go s.sendNotificationAsync(webhook.Project.Name, s.extractBranchName(webhook.Ref), "push", title, content.String())
}

func (s *ReviewService) sendMRNotification(webhook *repository.GitLabWebhook, reviewResult string) {
	var content strings.Builder
	content.WriteString(fmt.Sprintf("### 🔀 %s: Merge Request\n\n", webhook.Project.Name))
	content.WriteString("#### 合并请求信息:\n")
	content.WriteString(fmt.Sprintf("- **提交者**: %s\n", webhook.User.Username))
	content.WriteString(fmt.Sprintf("- **源分支**: %s\n", webhook.ObjectAttributes.SourceBranch))
	content.WriteString(fmt.Sprintf("- **目标分支**: %s\n", webhook.ObjectAttributes.TargetBranch))
	content.WriteString(fmt.Sprintf("- **标题**: %s\n", webhook.ObjectAttributes.Title))
	content.WriteString(fmt.Sprintf("- [查看合并详情](%s)\n\n", webhook.ObjectAttributes.URL))

	if reviewResult != "" {
		content.WriteString(fmt.Sprintf("#### AI Review 结果:\n%s\n\n", reviewResult))
	}

	title := "Merge Request Review"

	// 异步发送通知，避免影响主流程
	go s.sendNotificationAsync(webhook.Project.Name, webhook.ObjectAttributes.TargetBranch, "merge_request", title, content.String())
}

// HandleManualReview 处理手动审查请求
func (s *ReviewService) HandleManualReview(projectID int, sourceBranch, targetBranch, gitlabURL, gitlabToken string) error {
	logger.Infof("Processing manual review: project_id=%d, source=%s, target=%s", projectID, sourceBranch, targetBranch)

	// 使用提供的GitLab信息或配置中的默认值
	finalGitLabURL := gitlabURL
	if finalGitLabURL == "" {
		finalGitLabURL = s.config.GitLab.URL
		if finalGitLabURL == "" {
			finalGitLabURL = os.Getenv("GITLAB_URL")
		}
	}

	finalGitLabToken := gitlabToken
	if finalGitLabToken == "" {
		finalGitLabToken = s.config.GitLab.Token
		if finalGitLabToken == "" {
			finalGitLabToken = os.Getenv("GITLAB_ACCESS_TOKEN")
		}
	}

	if finalGitLabURL == "" || finalGitLabToken == "" {
		return fmt.Errorf("missing GitLab URL or token")
	}

	// 创建GitLab客户端
	gitlabClient := gitlab.NewClient(finalGitLabURL, finalGitLabToken, fmt.Sprintf("%d", projectID))

	// 获取项目信息
	project, err := gitlabClient.GetProject()
	if err != nil {
		logger.Errorf("Failed to get project info: %v", err)
		return fmt.Errorf("failed to get project info: %w", err)
	}

	// 获取两个分支之间的差异
	logger.Infof("Comparing branches: %s..%s", targetBranch, sourceBranch)
	changes, err := gitlabClient.CompareBranches(targetBranch, sourceBranch)
	if err != nil {
		logger.Errorf("Failed to compare branches: %v", err)
		return fmt.Errorf("failed to compare branches: %w", err)
	}

	// 过滤变更
	filteredChanges := gitlab.FilterChanges(changes)
	if len(filteredChanges) == 0 {
		logger.Info("No code changes detected between branches")
		return nil
	}

	// 进行代码审查
	logger.Infof("开始手动审查代码，共 %d 个文件变更", len(filteredChanges))

	// 构建提交信息（手动审查没有具体的提交信息）
	commitsText := fmt.Sprintf("手动审查: %s -> %s", sourceBranch, targetBranch)

	reviewResult, fileCount, err := s.reviewCodeManual(filteredChanges, commitsText, gitlabClient, sourceBranch, sourceBranch)
	if err != nil {
		logger.Errorf("Failed to review code: %v", err)
		return fmt.Errorf("failed to review code: %w", err)
	}

	logger.Infof("手动代码审查完成，结果长度: %d 字符", len(reviewResult))

	// 保存审查记录
	pushLog := &repository.PushReviewLog{
		ProjectName:  project.Name,
		Author:       "manual-review",
		Branch:       fmt.Sprintf("%s->%s", sourceBranch, targetBranch),
		BeforeCommit: targetBranch,
		AfterCommit:  sourceBranch,
		ReviewResult: reviewResult,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	logger.Infof("保存手动审查记录到数据库: %s", project.Name)
	if err := s.repo.InsertPushReviewLog(pushLog); err != nil {
		logger.Errorf("Failed to save manual review log: %v", err)
	} else {
		logger.Info("手动审查记录保存成功")
	}

	// 发送通知
	logger.Info("发送手动审查钉钉通知...")
	s.sendManualReviewNotification(project.Name, sourceBranch, targetBranch, reviewResult, fileCount)

	return nil
}

// reviewCodeManual 手动审查代码
func (s *ReviewService) reviewCodeManual(changes []repository.GitLabChange, commitsText string, gitlabClient *gitlab.Client, ref string, branchName string) (string, int, error) {
	// 创建统一审查引擎
	if s.unifiedReviewEngine == nil {
		s.unifiedReviewEngine = NewUnifiedReviewEngine(s.config, s.llmClient)
	}

	// 构建审查请求
	request := &ReviewRequest{
		Changes:      changes,
		Commits:      commitsText,
		GitlabClient: gitlabClient,
		Ref:          ref,
		BranchName:   branchName,
		ReviewType:   ReviewTypeEnhanced,
	}

	// 执行统一审查
	logger.Infof("调用LLM进行手动代码审查，使用模型: %s", s.config.LLM.Model)
	result, err := s.unifiedReviewEngine.ReviewCode(request)
	if err != nil {
		return "", len(changes), fmt.Errorf("手动代码审查失败: %w", err)
	}

	return result.Result, result.FileCount, nil
}

// sendManualReviewNotification 发送手动审查通知
func (s *ReviewService) sendManualReviewNotification(projectName, sourceBranch, targetBranch, reviewResult string, fileCount int) {
	var content strings.Builder
	content.WriteString(fmt.Sprintf("### 🔍 %s: 手动代码审查\n\n", projectName))
	content.WriteString("#### 审查信息:\n")
	content.WriteString(fmt.Sprintf("- **源分支**: %s\n", sourceBranch))
	content.WriteString(fmt.Sprintf("- **目标分支**: %s\n", targetBranch))
	content.WriteString(fmt.Sprintf("- **收集文件数**: %d 个文件\n", fileCount))
	content.WriteString(fmt.Sprintf("- **审查时间**: %s\n", notification.FormatChinaTime(time.Now().Format(time.RFC3339))))

	if reviewResult != "" {
		content.WriteString(fmt.Sprintf("\n#### AI Review 结果:\n%s\n\n", reviewResult))
	}

	title := fmt.Sprintf("%s 手动代码审查", projectName)

	// 手动审查通知默认使用默认配置，因为手动触发通常不区分具体的分支配置
	if err := s.notifier.SendMarkdown(title, content.String(), false); err != nil {
		logger.Errorf("Failed to send manual review notification: %v", err)
	} else {
		logger.Infof("钉钉手动审查通知发送成功: %s", projectName)
	}
}

// HandleManualReviewByCommit 处理基于项目名称和commit的手动审查请求
func (s *ReviewService) HandleManualReviewByCommit(projectName, fromCommit, toCommit, branch, gitlabURL, gitlabToken string) error {
	logger.Infof("Processing commit-based manual review: project=%s, from=%s, to=%s, branch=%s",
		projectName, safeSubstring(fromCommit, 8), safeSubstring(toCommit, 8), branch)

	// 使用提供的GitLab信息或配置中的默认值
	finalGitLabURL := gitlabURL
	if finalGitLabURL == "" {
		finalGitLabURL = s.config.GitLab.URL
		if finalGitLabURL == "" {
			finalGitLabURL = os.Getenv("GITLAB_URL")
		}
	}

	finalGitLabToken := gitlabToken
	if finalGitLabToken == "" {
		finalGitLabToken = s.config.GitLab.Token
		if finalGitLabToken == "" {
			finalGitLabToken = os.Getenv("GITLAB_ACCESS_TOKEN")
		}
	}

	if finalGitLabURL == "" || finalGitLabToken == "" {
		return fmt.Errorf("missing GitLab URL or token")
	}

	// 首先需要通过项目名称找到项目ID
	projectID, err := s.findProjectIDByName(finalGitLabURL, finalGitLabToken, projectName)
	if err != nil {
		logger.Errorf("Failed to find project ID for %s: %v", projectName, err)
		return fmt.Errorf("failed to find project ID: %w", err)
	}

	// 创建GitLab客户端
	gitlabClient := gitlab.NewClient(finalGitLabURL, finalGitLabToken, fmt.Sprintf("%d", projectID))

	// 确定使用的分支
	finalBranch := branch
	if finalBranch == "" {
		// 尝试根据commit获取分支
		logger.Infof("尝试根据commit获取分支: %s", safeSubstring(toCommit, 8))
		branches, err := gitlabClient.GetCommitBranches(toCommit)
		if err != nil {
			logger.Warnf("无法获取commit所在分支: %v", err)
		} else if len(branches) > 0 {
			// 优先选择master/main分支，否则选择第一个
			for _, b := range branches {
				if b == "master" || b == "main" {
					finalBranch = b
					break
				}
			}
			if finalBranch == "" {
				finalBranch = branches[0]
			}
			logger.Infof("自动检测到分支: %s", finalBranch)
		}

		// 如果还是没有分支，使用项目默认分支
		if finalBranch == "" {
			defaultBranch, err := gitlabClient.GetDefaultBranch()
			if err != nil {
				logger.Warnf("无法获取项目默认分支: %v", err)
				finalBranch = "master" // 最后的默认值
			} else {
				finalBranch = defaultBranch
			}
			logger.Infof("使用项目默认分支: %s", finalBranch)
		}
	} else {
		logger.Infof("使用用户指定的分支: %s", finalBranch)
	}

	// 验证commit是否存在
	logger.Infof("验证目标commit是否存在: %s", safeSubstring(toCommit, 8))
	_, err = gitlabClient.GetCommit(toCommit)
	if err != nil {
		logger.Errorf("目标commit不存在或无法访问: %s, 错误: %v", toCommit, err)
		return fmt.Errorf("目标commit不存在或无法访问: %s, 错误: %w", safeSubstring(toCommit, 8), err)
	}

	logger.Infof("验证起始commit是否存在: %s", safeSubstring(fromCommit, 8))
	_, err = gitlabClient.GetCommit(fromCommit)
	if err != nil {
		logger.Errorf("起始commit不存在或无法访问: %s, 错误: %v", fromCommit, err)
		return fmt.Errorf("起始commit不存在或无法访问: %s, 错误: %w", safeSubstring(fromCommit, 8), err)
	}

	// 获取两个commit之间的差异
	logger.Infof("Comparing commits: %s..%s", safeSubstring(fromCommit, 8), safeSubstring(toCommit, 8))
	changes, err := gitlabClient.CompareCommits(fromCommit, toCommit)
	if err != nil {
		logger.Errorf("Failed to compare commits: %v", err)
		return fmt.Errorf("failed to compare commits: %w", err)
	}

	// 过滤变更
	filteredChanges := gitlab.FilterChanges(changes)
	if len(filteredChanges) == 0 {
		logger.Info("No code changes detected between commits")
		return nil
	}

	// 进行代码审查 - 使用确定的分支
	logger.Infof("开始基于commit的手动审查代码，共 %d 个文件变更，使用分支: %s", len(filteredChanges), finalBranch)

	// 构建提交信息（基于commit的审查）
	commitsText := fmt.Sprintf("手动审查: %s -> %s (分支: %s)", safeSubstring(fromCommit, 8), safeSubstring(toCommit, 8), finalBranch)

	reviewResult, fileCount, err := s.reviewCodeManual(filteredChanges, commitsText, gitlabClient, toCommit, finalBranch)
	if err != nil {
		logger.Errorf("Failed to review code: %v", err)
		return fmt.Errorf("failed to review code: %w", err)
	}

	logger.Infof("基于commit的手动代码审查完成，结果长度: %d 字符", len(reviewResult))

	// 保存审查记录
	pushLog := &repository.PushReviewLog{
		ProjectName:  projectName,
		Author:       "manual-review",
		Branch:       fmt.Sprintf("%s->%s (%s)", safeSubstring(fromCommit, 8), safeSubstring(toCommit, 8), finalBranch),
		BeforeCommit: fromCommit,
		AfterCommit:  toCommit,
		ReviewResult: reviewResult,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	logger.Infof("保存基于commit的手动审查记录到数据库: %s", projectName)
	if err := s.repo.InsertPushReviewLog(pushLog); err != nil {
		logger.Errorf("Failed to save manual review log: %v", err)
	} else {
		logger.Info("基于commit的手动审查记录保存成功")
	}

	// 发送通知
	logger.Info("发送基于commit的手动审查钉钉通知...")
	s.sendCommitBasedManualReviewNotification(projectName, fromCommit, toCommit, reviewResult, fileCount)

	return nil
}

// findProjectIDByName 通过项目名称查找项目ID
func (s *ReviewService) findProjectIDByName(gitlabURL, gitlabToken, projectName string) (int, error) {
	// 创建临时客户端用于搜索项目
	tempClient := gitlab.NewClient(gitlabURL, gitlabToken, "")

	// 搜索项目
	projectID, err := tempClient.FindProjectByName(projectName)
	if err != nil {
		return 0, fmt.Errorf("failed to find project %s: %w", projectName, err)
	}

	return projectID, nil
}

// sendCommitBasedManualReviewNotification 发送基于commit的手动审查通知
func (s *ReviewService) sendCommitBasedManualReviewNotification(projectName, fromCommit, toCommit, reviewResult string, fileCount int) {
	var content strings.Builder
	content.WriteString(fmt.Sprintf("### 🔍 %s: 手动代码审查 (基于Commit)\n\n", projectName))
	content.WriteString("#### 审查信息:\n")
	content.WriteString(fmt.Sprintf("- **起始Commit**: %s\n", safeSubstring(fromCommit, 8)))
	content.WriteString(fmt.Sprintf("- **结束Commit**: %s\n", safeSubstring(toCommit, 8)))
	content.WriteString(fmt.Sprintf("- **收集文件数**: %d 个文件\n", fileCount))
	content.WriteString(fmt.Sprintf("- **审查时间**: %s\n", notification.FormatChinaTime(time.Now().Format(time.RFC3339))))

	if reviewResult != "" {
		content.WriteString(fmt.Sprintf("\n#### AI Review 结果:\n%s\n\n", reviewResult))
	}

	title := fmt.Sprintf("%s 手动代码审查 (Commit)", projectName)
	if err := s.notifier.SendMarkdown(title, content.String(), false); err != nil {
		logger.Errorf("Failed to send commit-based manual review notification: %v", err)
	} else {
		logger.Infof("钉钉基于commit的手动审查通知发送成功: %s", projectName)
	}
}

// HandleSingleCommitReview 处理单个commit的审查请求
func (s *ReviewService) HandleSingleCommitReview(projectName, commit, branch, gitlabURL, gitlabToken string) error {
	logger.Infof("Processing single commit review: project=%s, commit=%s, branch=%s",
		projectName, safeSubstring(commit, 8), branch)

	// 使用提供的GitLab信息或配置中的默认值
	finalGitLabURL := gitlabURL
	if finalGitLabURL == "" {
		finalGitLabURL = s.config.GitLab.URL
		if finalGitLabURL == "" {
			finalGitLabURL = os.Getenv("GITLAB_URL")
		}
	}

	finalGitLabToken := gitlabToken
	if finalGitLabToken == "" {
		finalGitLabToken = s.config.GitLab.Token
		if finalGitLabToken == "" {
			finalGitLabToken = os.Getenv("GITLAB_ACCESS_TOKEN")
		}
	}

	if finalGitLabURL == "" || finalGitLabToken == "" {
		return fmt.Errorf("missing GitLab URL or token")
	}

	// 首先需要通过项目名称找到项目ID
	projectID, err := s.findProjectIDByName(finalGitLabURL, finalGitLabToken, projectName)
	if err != nil {
		logger.Errorf("Failed to find project ID for %s: %v", projectName, err)
		return fmt.Errorf("failed to find project ID: %w", err)
	}

	// 创建GitLab客户端
	gitlabClient := gitlab.NewClient(finalGitLabURL, finalGitLabToken, fmt.Sprintf("%d", projectID))

	// 验证commit是否存在并获取commit信息
	logger.Infof("验证commit是否存在: %s", safeSubstring(commit, 8))
	commitInfo, err := gitlabClient.GetCommit(commit)
	if err != nil {
		logger.Errorf("commit不存在或无法访问: %s, 错误: %v", commit, err)
		return fmt.Errorf("commit不存在或无法访问: %s, 错误: %w", safeSubstring(commit, 8), err)
	}

	// 确定使用的分支
	finalBranch := branch
	if finalBranch == "" {
		// 尝试根据commit获取分支
		logger.Infof("尝试根据commit获取分支: %s", safeSubstring(commit, 8))
		branches, err := gitlabClient.GetCommitBranches(commit)
		if err != nil {
			logger.Warnf("无法获取commit所在分支: %v", err)
		} else if len(branches) > 0 {
			// 优先选择master/main分支，否则选择第一个
			for _, b := range branches {
				if b == "master" || b == "main" {
					finalBranch = b
					break
				}
			}
			if finalBranch == "" {
				finalBranch = branches[0]
			}
			logger.Infof("自动检测到分支: %s", finalBranch)
		}

		// 如果还是没有分支，使用项目默认分支
		if finalBranch == "" {
			defaultBranch, err := gitlabClient.GetDefaultBranch()
			if err != nil {
				logger.Warnf("无法获取项目默认分支: %v", err)
				finalBranch = "master" // 最后的默认值
			} else {
				finalBranch = defaultBranch
			}
			logger.Infof("使用项目默认分支: %s", finalBranch)
		}
	} else {
		logger.Infof("使用用户指定的分支: %s", finalBranch)
	}

	// 🔧 直接获取单个commit的差异，不需要parent commit
	logger.Infof("获取单个commit的变更: %s", safeSubstring(commit, 8))
	changes, err := gitlabClient.GetCommitDiff(commit)
	if err != nil {
		logger.Errorf("Failed to get commit changes: %v", err)
		return fmt.Errorf("failed to get commit changes: %w", err)
	}

	// 过滤变更
	filteredChanges := gitlab.FilterChanges(changes)
	if len(filteredChanges) == 0 {
		logger.Info("No code changes detected in commit")
		return nil
	}

	// 进行代码审查
	logger.Infof("开始单个commit审查，共 %d 个文件变更，使用分支: %s", len(filteredChanges), finalBranch)

	// 构建提交信息（单个commit审查）
	commitsText := fmt.Sprintf("单个commit审查: %s (分支: %s)\nCommit信息: %s",
		safeSubstring(commit, 8), finalBranch, commitInfo.Title)

	reviewResult, fileCount, err := s.reviewCodeManual(filteredChanges, commitsText, gitlabClient, commit, finalBranch)
	if err != nil {
		logger.Errorf("Failed to review code: %v", err)
		return fmt.Errorf("failed to review code: %w", err)
	}

	logger.Infof("单个commit审查完成，结果长度: %d 字符", len(reviewResult))

	// 保存审查记录
	pushLog := &repository.PushReviewLog{
		ProjectName:  projectName,
		Author:       "manual-review",
		Branch:       fmt.Sprintf("single-commit:%s (%s)", safeSubstring(commit, 8), finalBranch),
		BeforeCommit: commit,
		AfterCommit:  commit,
		ReviewResult: reviewResult,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	logger.Infof("保存单个commit审查记录到数据库: %s", projectName)
	if err := s.repo.InsertPushReviewLog(pushLog); err != nil {
		logger.Errorf("Failed to save single commit review log: %v", err)
	} else {
		logger.Info("单个commit审查记录保存成功")
	}

	// 发送通知
	logger.Info("发送单个commit审查钉钉通知...")
	s.sendSingleCommitReviewNotification(projectName, commit, commitInfo.Title, reviewResult, fileCount)

	return nil
}

// sendSingleCommitReviewNotification 发送单个commit审查通知
func (s *ReviewService) sendSingleCommitReviewNotification(projectName, commit, commitTitle, reviewResult string, fileCount int) {
	var content strings.Builder
	content.WriteString(fmt.Sprintf("### 🔍 %s: 单个Commit审查\n\n", projectName))
	content.WriteString("#### 审查信息:\n")
	content.WriteString(fmt.Sprintf("- **Commit**: %s\n", safeSubstring(commit, 8)))
	content.WriteString(fmt.Sprintf("- **标题**: %s\n", commitTitle))
	content.WriteString(fmt.Sprintf("- **收集文件数**: %d 个文件\n", fileCount))
	content.WriteString(fmt.Sprintf("- **审查时间**: %s\n", notification.FormatChinaTime(time.Now().Format(time.RFC3339))))

	if reviewResult != "" {
		content.WriteString(fmt.Sprintf("\n#### AI Review 结果:\n%s\n\n", reviewResult))
	}

	title := fmt.Sprintf("%s 单个Commit审查", projectName)
	if err := s.notifier.SendMarkdown(title, content.String(), false); err != nil {
		logger.Errorf("Failed to send single commit review notification: %v", err)
	} else {
		logger.Infof("钉钉单个commit审查通知发送成功: %s", projectName)
	}
}

// HandleManualReviewByProjectName 处理基于项目名称的分支比较审查请求
func (s *ReviewService) HandleManualReviewByProjectName(projectName, sourceBranch, targetBranch, gitlabURL, gitlabToken string) error {
	logger.Infof("Processing branch-based manual review by project name: project=%s, source=%s, target=%s",
		projectName, sourceBranch, targetBranch)

	// 使用提供的GitLab信息或配置中的默认值
	finalGitLabURL := gitlabURL
	if finalGitLabURL == "" {
		finalGitLabURL = s.config.GitLab.URL
		if finalGitLabURL == "" {
			finalGitLabURL = os.Getenv("GITLAB_URL")
		}
	}

	finalGitLabToken := gitlabToken
	if finalGitLabToken == "" {
		finalGitLabToken = s.config.GitLab.Token
		if finalGitLabToken == "" {
			finalGitLabToken = os.Getenv("GITLAB_ACCESS_TOKEN")
		}
	}

	if finalGitLabURL == "" || finalGitLabToken == "" {
		return fmt.Errorf("missing GitLab URL or token")
	}

	// 首先需要通过项目名称找到项目ID
	projectID, err := s.findProjectIDByName(finalGitLabURL, finalGitLabToken, projectName)
	if err != nil {
		logger.Errorf("Failed to find project ID for %s: %v", projectName, err)
		return fmt.Errorf("failed to find project ID: %w", err)
	}

	// 创建GitLab客户端
	gitlabClient := gitlab.NewClient(finalGitLabURL, finalGitLabToken, fmt.Sprintf("%d", projectID))

	// 获取两个分支之间的差异
	logger.Infof("Comparing branches: %s..%s", targetBranch, sourceBranch)
	changes, err := gitlabClient.CompareBranches(targetBranch, sourceBranch)
	if err != nil {
		logger.Errorf("Failed to compare branches: %v", err)
		return fmt.Errorf("failed to compare branches: %w", err)
	}

	// 过滤变更
	filteredChanges := gitlab.FilterChanges(changes)
	if len(filteredChanges) == 0 {
		logger.Info("No code changes detected between branches")
		return nil
	}

	// 进行代码审查
	logger.Infof("开始基于项目名称的分支比较审查，共 %d 个文件变更", len(filteredChanges))

	// 构建提交信息（分支比较审查）
	commitsText := fmt.Sprintf("分支比较审查: %s -> %s (项目: %s)", sourceBranch, targetBranch, projectName)

	reviewResult, fileCount, err := s.reviewCodeManual(filteredChanges, commitsText, gitlabClient, sourceBranch, sourceBranch)
	if err != nil {
		logger.Errorf("Failed to review code: %v", err)
		return fmt.Errorf("failed to review code: %w", err)
	}

	logger.Infof("基于项目名称的分支比较审查完成，结果长度: %d 字符", len(reviewResult))

	// 保存审查记录
	pushLog := &repository.PushReviewLog{
		ProjectName:  projectName,
		Author:       "manual-review",
		Branch:       fmt.Sprintf("branch-compare:%s->%s", sourceBranch, targetBranch),
		BeforeCommit: targetBranch,
		AfterCommit:  sourceBranch,
		ReviewResult: reviewResult,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	logger.Infof("保存基于项目名称的分支比较审查记录到数据库: %s", projectName)
	if err := s.repo.InsertPushReviewLog(pushLog); err != nil {
		logger.Errorf("Failed to save manual review log: %v", err)
	} else {
		logger.Info("基于项目名称的分支比较审查记录保存成功")
	}

	// 发送通知
	logger.Info("发送基于项目名称的分支比较审查钉钉通知...")
	s.sendBranchCompareReviewNotification(projectName, sourceBranch, targetBranch, reviewResult, fileCount)

	return nil
}

// sendBranchCompareReviewNotification 发送分支比较审查通知
func (s *ReviewService) sendBranchCompareReviewNotification(projectName, sourceBranch, targetBranch, reviewResult string, fileCount int) {
	var content strings.Builder
	content.WriteString(fmt.Sprintf("### 🔍 %s: 分支比较审查\n\n", projectName))
	content.WriteString("#### 审查信息:\n")
	content.WriteString(fmt.Sprintf("- **源分支**: %s\n", sourceBranch))
	content.WriteString(fmt.Sprintf("- **目标分支**: %s\n", targetBranch))
	content.WriteString(fmt.Sprintf("- **收集文件数**: %d 个文件\n", fileCount))
	content.WriteString(fmt.Sprintf("- **审查时间**: %s\n", notification.FormatChinaTime(time.Now().Format(time.RFC3339))))

	if reviewResult != "" {
		content.WriteString(fmt.Sprintf("\n#### AI Review 结果:\n%s\n\n", reviewResult))
	}

	title := fmt.Sprintf("%s 分支比较审查", projectName)
	if err := s.notifier.SendMarkdown(title, content.String(), false); err != nil {
		logger.Errorf("Failed to send branch compare review notification: %v", err)
	} else {
		logger.Infof("钉钉分支比较审查通知发送成功: %s", projectName)
	}
}
