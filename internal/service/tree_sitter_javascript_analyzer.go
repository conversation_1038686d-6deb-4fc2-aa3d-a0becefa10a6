package service

import (
	"fmt"
	"strings"

	"ai-codereview-service/pkg/logger"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/javascript"
)

// JavaScriptTreeSitterAnalyzer JavaScript专用Tree-sitter分析器
type JavaScriptTreeSitterAnalyzer struct {
	*BaseTreeSitterAnalyzer
}

// NewJavaScriptTreeSitterAnalyzer 创建JavaScript Tree-sitter分析器
func NewJavaScriptTreeSitterAnalyzer() *JavaScriptTreeSitterAnalyzer {
	jsLanguage := javascript.GetLanguage()
	base := NewBaseTreeSitterAnalyzer(jsLanguage)

	return &JavaScriptTreeSitterAnalyzer{
		BaseTreeSitterAnalyzer: base,
	}
}

// ExtractFunctionCalls 提取JavaScript函数调用
func (analyzer *JavaScriptTreeSitterAnalyzer) ExtractFunctionCalls(tree *sitter.Tree) ([]MethodCall, error) {
	// JavaScript函数调用查询
	query := `
	(call_expression
	  function: (identifier) @function) @call
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query function calls: %w", err)
	}

	var functionCalls []MethodCall
	for _, node := range nodes {
		functionCall := analyzer.parseJSFunctionCall(node)
		if functionCall != nil {
			functionCalls = append(functionCalls, *functionCall)
		}
	}

	return functionCalls, nil
}

// ExtractFunctions 提取JavaScript函数定义
func (analyzer *JavaScriptTreeSitterAnalyzer) ExtractFunctions(tree *sitter.Tree) ([]MethodInfo, error) {
	// JavaScript函数声明查询
	query := `
	(function_declaration
	  name: (identifier) @func_name
	  parameters: (formal_parameters)? @params
	  body: (statement_block)? @body) @function
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query functions: %w", err)
	}

	var functions []MethodInfo
	for _, node := range nodes {
		functionInfo := analyzer.parseJSFunctionInfo(node)
		if functionInfo != nil {
			functions = append(functions, *functionInfo)
		}
	}

	return functions, nil
}

// ExtractImports 提取JavaScript导入信息
func (analyzer *JavaScriptTreeSitterAnalyzer) ExtractImports(tree *sitter.Tree) ([]ImportInfo, error) {
	// JavaScript导入声明查询
	query := `
	(import_statement
	  source: (string) @import_path) @import
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query imports: %w", err)
	}

	var imports []ImportInfo
	for _, node := range nodes {
		importInfo := analyzer.parseJSImportInfo(node)
		if importInfo != nil {
			imports = append(imports, *importInfo)
		}
	}

	return imports, nil
}

// DetectEventListenerLeaks 检测事件监听器泄漏
func (analyzer *JavaScriptTreeSitterAnalyzer) DetectEventListenerLeaks(tree *sitter.Tree) ([]string, error) {
	// 检测addEventListener但没有对应removeEventListener
	query := `
	(call_expression
	  function: (member_expression
	    property: (property_identifier) @method
	    (#eq? @method "addEventListener"))) @add_listener
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect event listener leaks: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		position := analyzer.GetPosition(node)

		// 检查是否有对应的移除逻辑（简化版）
		if !analyzer.hasRemoveEventListener(node) {
			issues = append(issues, fmt.Sprintf("疑似事件监听器泄漏: 第%d行，addEventListener可能未正确移除",
				position.StartRow+1))
		}
	}

	return issues, nil
}

// DetectInfiniteLoops 检测无限循环风险
func (analyzer *JavaScriptTreeSitterAnalyzer) DetectInfiniteLoops(tree *sitter.Tree) ([]string, error) {
	// 检测while(true)或for(;;)
	query := `
	(while_statement
	  condition: (true)) @infinite_while
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect infinite loops: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		position := analyzer.GetPosition(node)

		// 检查是否有break语句
		if !analyzer.hasBreakStatement(node) {
			issues = append(issues, fmt.Sprintf("疑似无限循环: 第%d行，while(true)循环缺少break条件",
				position.StartRow+1))
		}
	}

	return issues, nil
}

// DetectXSSRisks 检测XSS风险
func (analyzer *JavaScriptTreeSitterAnalyzer) DetectXSSRisks(tree *sitter.Tree) ([]string, error) {
	// 检测innerHTML的使用
	query := `
	(assignment_expression
	  left: (member_expression
	    property: (property_identifier) @prop
	    (#eq? @prop "innerHTML"))) @innerHTML_assignment
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect XSS risks: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		position := analyzer.GetPosition(node)
		issues = append(issues, fmt.Sprintf("XSS风险: 第%d行，innerHTML赋值可能导致XSS攻击",
			position.StartRow+1))
	}

	return issues, nil
}

// DetectReactHookIssues 检测React Hook问题
func (analyzer *JavaScriptTreeSitterAnalyzer) DetectReactHookIssues(tree *sitter.Tree) ([]string, error) {
	// 检测条件中的Hook调用
	rootNode := tree.RootNode()
	var issues []string

	analyzer.WalkTree(rootNode, func(node *sitter.Node) bool {
		if node.Type() == "call_expression" {
			callText := analyzer.GetNodeText(node)

			// 检测Hook调用
			if strings.Contains(callText, "useState") || strings.Contains(callText, "useEffect") {
				// 检查是否在条件语句中
				if analyzer.isInConditional(node) {
					position := analyzer.GetPosition(node)
					issues = append(issues, fmt.Sprintf("React Hook规则违反: 第%d行，Hook不能在条件语句中调用",
						position.StartRow+1))
				}
			}
		}
		return true
	})

	return issues, nil
}

// DetectAsyncAwaitIssues 检测异步/等待问题
func (analyzer *JavaScriptTreeSitterAnalyzer) DetectAsyncAwaitIssues(tree *sitter.Tree) ([]string, error) {
	// 检测async函数中的同步操作
	query := `
	(function_declaration
	  (async) @async
	  body: (statement_block) @body) @async_function
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect async/await issues: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		// 检查async函数内是否有阻塞操作
		if analyzer.hasBlockingOperations(node) {
			position := analyzer.GetPosition(node)
			issues = append(issues, fmt.Sprintf("异步函数问题: 第%d行，async函数中存在可能的阻塞操作",
				position.StartRow+1))
		}
	}

	return issues, nil
}

// 辅助方法
func (analyzer *JavaScriptTreeSitterAnalyzer) parseJSFunctionCall(node *sitter.Node) *MethodCall {
	if node == nil {
		return nil
	}

	functionNode := analyzer.FindChildByType(node, "identifier")
	if functionNode == nil {
		return nil
	}

	functionName := analyzer.GetNodeText(functionNode)
	position := analyzer.GetPosition(node)

	return &MethodCall{
		Method:     functionName,
		LineNumber: position.StartRow + 1,
		Position:   position,
	}
}

func (analyzer *JavaScriptTreeSitterAnalyzer) parseJSFunctionInfo(node *sitter.Node) *MethodInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "identifier")
	if nameNode == nil {
		return nil
	}

	functionName := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	// 解析参数
	var parameters []string
	paramsNode := analyzer.FindChildByType(node, "formal_parameters")
	if paramsNode != nil {
		paramText := analyzer.GetNodeText(paramsNode)
		parameters = append(parameters, paramText)
	}

	// 获取函数体
	bodyNode := analyzer.FindChildByType(node, "statement_block")
	bodyText := ""
	if bodyNode != nil {
		bodyText = analyzer.GetNodeText(bodyNode)
	}

	return &MethodInfo{
		Name:       functionName,
		Parameters: parameters,
		Position:   position,
		Body:       bodyText,
	}
}

func (analyzer *JavaScriptTreeSitterAnalyzer) parseJSImportInfo(node *sitter.Node) *ImportInfo {
	if node == nil {
		return nil
	}

	pathNode := analyzer.FindChildByType(node, "string")
	if pathNode == nil {
		return nil
	}

	importPath := strings.Trim(analyzer.GetNodeText(pathNode), `"'`)
	position := analyzer.GetPosition(node)

	return &ImportInfo{
		Package:  importPath,
		Position: position,
	}
}

func (analyzer *JavaScriptTreeSitterAnalyzer) hasRemoveEventListener(node *sitter.Node) bool {
	// 简化版：检查是否在同一作用域有removeEventListener调用
	// 实际实现需要更复杂的作用域分析
	return false
}

func (analyzer *JavaScriptTreeSitterAnalyzer) hasBreakStatement(node *sitter.Node) bool {
	// 检查循环体内是否有break语句
	hasBreak := false
	analyzer.WalkTree(node, func(n *sitter.Node) bool {
		if n.Type() == "break_statement" {
			hasBreak = true
			return false
		}
		return true
	})
	return hasBreak
}

func (analyzer *JavaScriptTreeSitterAnalyzer) isInConditional(node *sitter.Node) bool {
	// 检查节点是否在条件语句中
	current := node.Parent()
	for current != nil {
		if current.Type() == "if_statement" || current.Type() == "for_statement" {
			return true
		}
		current = current.Parent()
	}
	return false
}

func (analyzer *JavaScriptTreeSitterAnalyzer) hasBlockingOperations(node *sitter.Node) bool {
	// 检查是否有同步阻塞操作
	hasBlocking := false
	analyzer.WalkTree(node, func(n *sitter.Node) bool {
		if n.Type() == "call_expression" {
			callText := analyzer.GetNodeText(n)
			// 检查常见的阻塞操作
			if strings.Contains(callText, "setTimeout") ||
				strings.Contains(callText, "setInterval") {
				hasBlocking = true
				return false
			}
		}
		return true
	})
	return hasBlocking
}

// JavaScriptAnalysisResult JavaScript代码分析结果
type JavaScriptAnalysisResult struct {
	FunctionCalls      []MethodCall `json:"function_calls"`
	Functions          []MethodInfo `json:"functions"`
	Imports            []ImportInfo `json:"imports"`
	EventListenerLeaks []string     `json:"event_listener_leaks"`
	InfiniteLoops      []string     `json:"infinite_loops"`
	XSSRisks           []string     `json:"xss_risks"`
	ReactHookIssues    []string     `json:"react_hook_issues"`
	AsyncAwaitIssues   []string     `json:"async_await_issues"`
}

// AnalyzeJavaScriptCode 综合分析JavaScript代码
func (analyzer *JavaScriptTreeSitterAnalyzer) AnalyzeJavaScriptCode(code string) (*JavaScriptAnalysisResult, error) {
	logger.Infof("开始使用Tree-sitter分析JavaScript代码")

	// 解析代码
	tree, err := analyzer.ParseCode(code)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JavaScript code: %w", err)
	}

	// 记录解析统计
	analyzer.LogParsingStats(tree, "JavaScript")

	// 提取各种信息
	functionCalls, err := analyzer.ExtractFunctionCalls(tree)
	if err != nil {
		logger.Warnf("提取函数调用失败: %v", err)
	}

	functions, err := analyzer.ExtractFunctions(tree)
	if err != nil {
		logger.Warnf("提取函数定义失败: %v", err)
	}

	imports, err := analyzer.ExtractImports(tree)
	if err != nil {
		logger.Warnf("提取导入信息失败: %v", err)
	}

	// 检测各种问题
	eventLeaks, _ := analyzer.DetectEventListenerLeaks(tree)
	infiniteLoops, _ := analyzer.DetectInfiniteLoops(tree)
	xssRisks, _ := analyzer.DetectXSSRisks(tree)
	reactIssues, _ := analyzer.DetectReactHookIssues(tree)
	asyncIssues, _ := analyzer.DetectAsyncAwaitIssues(tree)

	result := &JavaScriptAnalysisResult{
		FunctionCalls:      functionCalls,
		Functions:          functions,
		Imports:            imports,
		EventListenerLeaks: eventLeaks,
		InfiniteLoops:      infiniteLoops,
		XSSRisks:           xssRisks,
		ReactHookIssues:    reactIssues,
		AsyncAwaitIssues:   asyncIssues,
	}

	logger.Infof("JavaScript代码分析完成: 函数调用%d个, 函数定义%d个, 导入%d个, 发现问题%d个",
		len(functionCalls), len(functions), len(imports),
		len(eventLeaks)+len(infiniteLoops)+len(xssRisks)+len(reactIssues)+len(asyncIssues))

	return result, nil
}
