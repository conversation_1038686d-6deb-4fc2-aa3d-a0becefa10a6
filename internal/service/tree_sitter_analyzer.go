package service

import (
	"fmt"
	"strings"

	"ai-codereview-service/pkg/logger"

	sitter "github.com/smacker/go-tree-sitter"
)

// MethodCall 表示方法调用信息
type MethodCall struct {
	Object     string   `json:"object"`
	Method     string   `json:"method"`
	Arguments  []string `json:"arguments"`
	LineNumber int      `json:"line_number"`
	Position   Position `json:"position"`
}

// Position 表示代码位置
type Position struct {
	StartByte int `json:"start_byte"`
	EndByte   int `json:"end_byte"`
	StartRow  int `json:"start_row"`
	StartCol  int `json:"start_col"`
	EndRow    int `json:"end_row"`
	EndCol    int `json:"end_col"`
}

// ClassInfo 表示类信息
type ClassInfo struct {
	Name        string   `json:"name"`
	Package     string   `json:"package"`
	Imports     []string `json:"imports"`
	Methods     []string `json:"methods"`
	Fields      []string `json:"fields"`
	Annotations []string `json:"annotations"`
	Position    Position `json:"position"`
}

// MethodInfo 表示方法信息
type MethodInfo struct {
	Name        string   `json:"name"`
	ReturnType  string   `json:"return_type"`
	Parameters  []string `json:"parameters"`
	Annotations []string `json:"annotations"`
	Position    Position `json:"position"`
	Body        string   `json:"body"`
}

// ImportInfo 表示导入信息
type ImportInfo struct {
	Package  string   `json:"package"`
	IsStatic bool     `json:"is_static"`
	IsWild   bool     `json:"is_wild"`
	Position Position `json:"position"`
}

// AnnotationInfo 表示注解信息
type AnnotationInfo struct {
	Name      string            `json:"name"`
	Arguments map[string]string `json:"arguments"`
	Position  Position          `json:"position"`
}

// TreeSitterAnalyzer Tree-sitter通用分析器接口
type TreeSitterAnalyzer interface {
	ParseCode(code string) (*sitter.Tree, error)
	ExtractMethodCalls(tree *sitter.Tree) ([]MethodCall, error)
	ExtractClasses(tree *sitter.Tree) ([]ClassInfo, error)
	ExtractMethods(tree *sitter.Tree) ([]MethodInfo, error)
	ExtractImports(tree *sitter.Tree) ([]ImportInfo, error)
	ExtractAnnotations(tree *sitter.Tree) ([]AnnotationInfo, error)
	QueryNodes(tree *sitter.Tree, query string) ([]*sitter.Node, error)
}

// BaseTreeSitterAnalyzer 基础Tree-sitter分析器
type BaseTreeSitterAnalyzer struct {
	parser   *sitter.Parser
	language *sitter.Language
	code     []byte
}

// NewBaseTreeSitterAnalyzer 创建基础Tree-sitter分析器
func NewBaseTreeSitterAnalyzer(language *sitter.Language) *BaseTreeSitterAnalyzer {
	parser := sitter.NewParser()
	parser.SetLanguage(language)

	return &BaseTreeSitterAnalyzer{
		parser:   parser,
		language: language,
	}
}

// ParseCode 解析代码生成语法树
func (analyzer *BaseTreeSitterAnalyzer) ParseCode(code string) (*sitter.Tree, error) {
	analyzer.code = []byte(code)
	tree := analyzer.parser.Parse(nil, analyzer.code)
	if tree == nil {
		return nil, fmt.Errorf("failed to parse code: tree is nil")
	}

	return tree, nil
}

// QueryNodes 使用查询语句提取节点
func (analyzer *BaseTreeSitterAnalyzer) QueryNodes(tree *sitter.Tree, queryStr string) ([]*sitter.Node, error) {
	query, err := sitter.NewQuery([]byte(queryStr), analyzer.language)
	if err != nil {
		return nil, fmt.Errorf("failed to create query: %w", err)
	}

	cursor := sitter.NewQueryCursor()
	cursor.Exec(query, tree.RootNode())

	var nodes []*sitter.Node
	for {
		match, ok := cursor.NextMatch()
		if !ok {
			break
		}

		for _, capture := range match.Captures {
			nodes = append(nodes, capture.Node)
		}
	}

	return nodes, nil
}

// GetNodeText 获取节点对应的文本内容
func (analyzer *BaseTreeSitterAnalyzer) GetNodeText(node *sitter.Node) string {
	if analyzer.code == nil || node == nil {
		return ""
	}

	startByte := node.StartByte()
	endByte := node.EndByte()

	if startByte >= uint32(len(analyzer.code)) || endByte > uint32(len(analyzer.code)) {
		return ""
	}

	return string(analyzer.code[startByte:endByte])
}

// GetPosition 获取节点位置信息
func (analyzer *BaseTreeSitterAnalyzer) GetPosition(node *sitter.Node) Position {
	if node == nil {
		return Position{}
	}

	return Position{
		StartByte: int(node.StartByte()),
		EndByte:   int(node.EndByte()),
		StartRow:  int(node.StartPoint().Row),
		StartCol:  int(node.StartPoint().Column),
		EndRow:    int(node.EndPoint().Row),
		EndCol:    int(node.EndPoint().Column),
	}
}

// FindChildByType 查找指定类型的子节点
func (analyzer *BaseTreeSitterAnalyzer) FindChildByType(node *sitter.Node, nodeType string) *sitter.Node {
	if node == nil {
		return nil
	}

	childCount := int(node.ChildCount())
	for i := 0; i < childCount; i++ {
		child := node.Child(i)
		if child.Type() == nodeType {
			return child
		}
	}

	return nil
}

// FindChildrenByType 查找所有指定类型的子节点
func (analyzer *BaseTreeSitterAnalyzer) FindChildrenByType(node *sitter.Node, nodeType string) []*sitter.Node {
	var children []*sitter.Node
	if node == nil {
		return children
	}

	childCount := int(node.ChildCount())
	for i := 0; i < childCount; i++ {
		child := node.Child(i)
		if child.Type() == nodeType {
			children = append(children, child)
		}
	}

	return children
}

// WalkTree 遍历语法树
func (analyzer *BaseTreeSitterAnalyzer) WalkTree(node *sitter.Node, visitor func(*sitter.Node) bool) {
	if node == nil {
		return
	}

	if !visitor(node) {
		return
	}

	childCount := int(node.ChildCount())
	for i := 0; i < childCount; i++ {
		child := node.Child(i)
		analyzer.WalkTree(child, visitor)
	}
}

// IsInLoop 检查节点是否在循环内
func (analyzer *BaseTreeSitterAnalyzer) IsInLoop(node *sitter.Node) bool {
	current := node.Parent()
	for current != nil {
		nodeType := current.Type()
		if strings.Contains(nodeType, "for") || strings.Contains(nodeType, "while") ||
			strings.Contains(nodeType, "loop") {
			return true
		}
		current = current.Parent()
	}
	return false
}

// GetContainingMethod 获取包含指定节点的方法节点
func (analyzer *BaseTreeSitterAnalyzer) GetContainingMethod(node *sitter.Node) *sitter.Node {
	current := node.Parent()
	for current != nil {
		if strings.Contains(current.Type(), "method") {
			return current
		}
		current = current.Parent()
	}
	return nil
}

// GetContainingClass 获取包含指定节点的类节点
func (analyzer *BaseTreeSitterAnalyzer) GetContainingClass(node *sitter.Node) *sitter.Node {
	current := node.Parent()
	for current != nil {
		if strings.Contains(current.Type(), "class") {
			return current
		}
		current = current.Parent()
	}
	return nil
}

// LogParsingStats 记录解析统计信息
func (analyzer *BaseTreeSitterAnalyzer) LogParsingStats(tree *sitter.Tree, language string) {
	if tree == nil {
		logger.Warnf("Tree-sitter解析失败: %s语言的语法树为空", language)
		return
	}

	rootNode := tree.RootNode()
	if rootNode.HasError() {
		logger.Warnf("Tree-sitter解析警告: %s语言的语法树包含错误节点", language)
	}

	logger.Infof("Tree-sitter解析成功: %s语言, 根节点类型: %s, 字节范围: %d-%d",
		language, rootNode.Type(), rootNode.StartByte(), rootNode.EndByte())
}

// Close 释放资源
func (analyzer *BaseTreeSitterAnalyzer) Close() {
	// smacker/go-tree-sitter 不需要手动关闭资源
}
