package service

import (
	"fmt"
	"strings"

	"ai-codereview-service/pkg/logger"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/java"
)

// JavaTreeSitterAnalyzer Java专用Tree-sitter分析器
type JavaTreeSitterAnalyzer struct {
	*BaseTreeSitterAnalyzer
}

// NewJavaTreeSitterAnalyzer 创建Java Tree-sitter分析器
func NewJavaTreeSitterAnalyzer() *JavaTreeSitterAnalyzer {
	javaLanguage := java.GetLanguage()
	base := NewBaseTreeSitterAnalyzer(javaLanguage)

	return &JavaTreeSitterAnalyzer{
		BaseTreeSitterAnalyzer: base,
	}
}

// ExtractMethodCalls 提取Java方法调用
func (analyzer *JavaTreeSitterAnalyzer) ExtractMethodCalls(tree *sitter.Tree) ([]MethodCall, error) {
	// Java方法调用查询
	query := `
	(method_invocation
	  object: (identifier)? @object
	  name: (identifier) @method
	  arguments: (argument_list) @args) @call
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query method calls: %w", err)
	}

	var methodCalls []MethodCall
	for _, node := range nodes {
		methodCall := analyzer.parseMethodCall(node)
		if methodCall != nil {
			methodCalls = append(methodCalls, *methodCall)
		}
	}

	return methodCalls, nil
}

// ExtractClasses 提取Java类信息
func (analyzer *JavaTreeSitterAnalyzer) ExtractClasses(tree *sitter.Tree) ([]ClassInfo, error) {
	// Java类声明查询
	query := `
	(class_declaration
	  name: (identifier) @class_name) @class
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query classes: %w", err)
	}

	var classes []ClassInfo
	for _, node := range nodes {
		classInfo := analyzer.parseClassInfo(node)
		if classInfo != nil {
			classes = append(classes, *classInfo)
		}
	}

	return classes, nil
}

// ExtractMethods 提取Java方法信息
func (analyzer *JavaTreeSitterAnalyzer) ExtractMethods(tree *sitter.Tree) ([]MethodInfo, error) {
	// Java方法声明查询
	query := `
	(method_declaration
	  name: (identifier) @method_name
	  parameters: (formal_parameters)? @params
	  body: (block)? @body) @method
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query methods: %w", err)
	}

	var methods []MethodInfo
	for _, node := range nodes {
		methodInfo := analyzer.parseMethodInfo(node)
		if methodInfo != nil {
			methods = append(methods, *methodInfo)
		}
	}

	return methods, nil
}

// ExtractImports 提取Java导入信息
func (analyzer *JavaTreeSitterAnalyzer) ExtractImports(tree *sitter.Tree) ([]ImportInfo, error) {
	// 修复的Java导入声明查询，分别处理普通导入和静态导入
	query := `
	(import_declaration
	  (scoped_identifier) @import_path) @import
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query imports: %w", err)
	}

	// 使用map收集所有导入，然后智能去重
	importCandidates := make(map[string][]*ImportInfo)

	for _, node := range nodes {
		importInfo := analyzer.parseImportInfo(node)
		if importInfo != nil && importInfo.Package != "" {
			// 按包路径分组
			importCandidates[importInfo.Package] = append(importCandidates[importInfo.Package], importInfo)
		}
	}

	// 智能去重：对于同一个包路径，优先选择静态导入标记正确的版本
	var imports []ImportInfo
	for _, candidates := range importCandidates {
		if len(candidates) == 1 {
			// 只有一个候选，直接使用
			imports = append(imports, *candidates[0])
		} else {
			// 多个候选，选择最准确的
			bestCandidate := analyzer.selectBestImportCandidate(candidates)
			if bestCandidate != nil {
				imports = append(imports, *bestCandidate)
			}
		}
	}

	return imports, nil
}

// selectBestImportCandidate 从多个候选中选择最准确的导入信息
func (analyzer *JavaTreeSitterAnalyzer) selectBestImportCandidate(candidates []*ImportInfo) *ImportInfo {
	if len(candidates) == 0 {
		return nil
	}

	if len(candidates) == 1 {
		return candidates[0]
	}

	// 优先级规则：
	// 1. 如果有静态导入标记的，优先选择静态导入
	// 2. 如果都是普通导入或都是静态导入，选择第一个

	var staticImport *ImportInfo
	var normalImport *ImportInfo

	for _, candidate := range candidates {
		if candidate.IsStatic {
			staticImport = candidate
		} else {
			normalImport = candidate
		}
	}

	// 如果同时存在静态和普通导入，说明这是一个静态导入被错误解析了
	// 优先返回静态导入版本
	if staticImport != nil {
		return staticImport
	}

	// 否则返回普通导入
	if normalImport != nil {
		return normalImport
	}

	// 兜底：返回第一个
	return candidates[0]
}

// ExtractAnnotations 提取Java注解信息
func (analyzer *JavaTreeSitterAnalyzer) ExtractAnnotations(tree *sitter.Tree) ([]AnnotationInfo, error) {
	// Java注解查询
	query := `
	(annotation
	  name: (identifier) @annotation_name
	  arguments: (annotation_argument_list)? @args) @annotation
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query annotations: %w", err)
	}

	var annotations []AnnotationInfo
	for _, node := range nodes {
		annotationInfo := analyzer.parseAnnotationInfo(node)
		if annotationInfo != nil {
			annotations = append(annotations, *annotationInfo)
		}
	}

	return annotations, nil
}

// DetectNPlusOneQueries 检测N+1查询问题
func (analyzer *JavaTreeSitterAnalyzer) DetectNPlusOneQueries(tree *sitter.Tree) ([]string, error) {
	// 检测for循环内的数据库查询方法调用
	query := `
	(for_statement
	  body: (block
	    (expression_statement
	      (method_invocation
	        name: (identifier) @method
	        (#match? @method "^(select|find|get|query).*"))))) @loop
	`

	nodes, err := analyzer.QueryNodes(tree, query)
	if err != nil {
		return nil, fmt.Errorf("failed to detect N+1 queries: %w", err)
	}

	var issues []string
	for _, node := range nodes {
		position := analyzer.GetPosition(node)
		methodName := analyzer.GetNodeText(analyzer.FindChildByType(node, "identifier"))
		issues = append(issues, fmt.Sprintf("疑似N+1查询问题: 第%d行，方法 %s 在循环内调用",
			position.StartRow+1, methodName))
	}

	return issues, nil
}

// DetectSpringAnnotations 检测Spring相关注解
func (analyzer *JavaTreeSitterAnalyzer) DetectSpringAnnotations(tree *sitter.Tree) ([]string, error) {
	// 简化版：直接在语法树中查找注解节点
	rootNode := tree.RootNode()
	var issues []string

	springAnnotations := []string{
		"Service", "Controller", "RestController", "Component", "Repository",
		"Autowired", "Resource", "Value", "RequestMapping", "GetMapping", "PostMapping",
		"DubboService", "DubboReference",
	}

	analyzer.WalkTree(rootNode, func(node *sitter.Node) bool {
		if node.Type() == "annotation" {
			nodeText := analyzer.GetNodeText(node)
			for _, annotation := range springAnnotations {
				if strings.Contains(nodeText, "@"+annotation) {
					position := analyzer.GetPosition(node)
					containingClass := analyzer.GetContainingClass(node)
					className := "未知类"
					if containingClass != nil {
						className = analyzer.GetNodeText(analyzer.FindChildByType(containingClass, "identifier"))
					}

					issues = append(issues, fmt.Sprintf("发现Spring注解 @%s: 第%d行，所在类: %s",
						annotation, position.StartRow+1, className))
					break
				}
			}
		}
		return true
	})

	return issues, nil
}

// DetectSQLInjectionRisks 检测SQL注入风险
func (analyzer *JavaTreeSitterAnalyzer) DetectSQLInjectionRisks(tree *sitter.Tree) ([]string, error) {
	// 检测字符串拼接构建SQL的风险模式
	rootNode := tree.RootNode()
	var issues []string

	analyzer.WalkTree(rootNode, func(node *sitter.Node) bool {
		if node.Type() == "binary_expression" {
			nodeText := analyzer.GetNodeText(node)
			// 简单检测包含SQL关键字的字符串拼接
			if strings.Contains(strings.ToLower(nodeText), "select") && strings.Contains(nodeText, "+") {
				position := analyzer.GetPosition(node)
				issues = append(issues, fmt.Sprintf("疑似SQL注入风险: 第%d行，SQL字符串拼接: %s",
					position.StartRow+1, nodeText))
			}
		}
		return true
	})

	return issues, nil
}

// 辅助方法
func (analyzer *JavaTreeSitterAnalyzer) parseMethodCall(node *sitter.Node) *MethodCall {
	if node == nil {
		return nil
	}

	methodNode := analyzer.FindChildByType(node, "identifier")
	if methodNode == nil {
		return nil
	}

	methodName := analyzer.GetNodeText(methodNode)
	position := analyzer.GetPosition(node)

	// 尝试获取对象名
	objectName := ""
	objectNode := analyzer.FindChildByType(node, "identifier")
	if objectNode != nil && objectNode != methodNode {
		objectName = analyzer.GetNodeText(objectNode)
	}

	// 解析参数
	var arguments []string
	argsNode := analyzer.FindChildByType(node, "argument_list")
	if argsNode != nil {
		argNodes := analyzer.FindChildrenByType(argsNode, "identifier")
		for _, argNode := range argNodes {
			arguments = append(arguments, analyzer.GetNodeText(argNode))
		}
	}

	return &MethodCall{
		Object:     objectName,
		Method:     methodName,
		Arguments:  arguments,
		LineNumber: position.StartRow + 1,
		Position:   position,
	}
}

func (analyzer *JavaTreeSitterAnalyzer) parseClassInfo(node *sitter.Node) *ClassInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "identifier")
	if nameNode == nil {
		return nil
	}

	className := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	return &ClassInfo{
		Name:     className,
		Position: position,
	}
}

func (analyzer *JavaTreeSitterAnalyzer) parseMethodInfo(node *sitter.Node) *MethodInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "identifier")
	if nameNode == nil {
		return nil
	}

	methodName := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	// 解析参数
	var parameters []string
	paramsNode := analyzer.FindChildByType(node, "formal_parameters")
	if paramsNode != nil {
		paramNodes := analyzer.FindChildrenByType(paramsNode, "formal_parameter")
		for _, paramNode := range paramNodes {
			paramText := analyzer.GetNodeText(paramNode)
			parameters = append(parameters, paramText)
		}
	}

	// 获取方法体
	bodyNode := analyzer.FindChildByType(node, "block")
	bodyText := ""
	if bodyNode != nil {
		bodyText = analyzer.GetNodeText(bodyNode)
	}

	return &MethodInfo{
		Name:       methodName,
		Parameters: parameters,
		Position:   position,
		Body:       bodyText,
	}
}

func (analyzer *JavaTreeSitterAnalyzer) parseImportInfo(node *sitter.Node) *ImportInfo {
	if node == nil {
		return nil
	}

	importText := analyzer.GetNodeText(node)
	position := analyzer.GetPosition(node)

	// 调试日志：记录原始导入文本
	logger.Debugf("解析导入语句: %s (位置: %d行)", importText, position.StartRow+1)

	// 检测是否为静态导入
	isStatic := strings.Contains(importText, "static")

	// 检测是否为通配符导入
	isWild := strings.Contains(importText, "*")

	// 提取包路径 - 改进解析逻辑
	packagePath := strings.TrimSpace(importText)

	// 移除import关键字
	if strings.HasPrefix(packagePath, "import") {
		packagePath = strings.TrimSpace(packagePath[6:])
	}

	// 移除static关键字
	if strings.HasPrefix(packagePath, "static") {
		packagePath = strings.TrimSpace(packagePath[6:])
	}

	// 移除分号
	packagePath = strings.TrimSuffix(packagePath, ";")

	// 清理多余的空白字符
	packagePath = strings.TrimSpace(packagePath)

	// 验证包路径是否有效
	if packagePath == "" {
		logger.Warnf("解析到空的包路径，原始文本: %s", importText)
		return nil
	}

	importInfo := &ImportInfo{
		Package:  packagePath,
		IsStatic: isStatic,
		IsWild:   isWild,
		Position: position,
	}

	logger.Debugf("解析结果: 包=%s, 静态=%t, 通配符=%t", packagePath, isStatic, isWild)
	return importInfo
}

func (analyzer *JavaTreeSitterAnalyzer) parseAnnotationInfo(node *sitter.Node) *AnnotationInfo {
	if node == nil {
		return nil
	}

	nameNode := analyzer.FindChildByType(node, "identifier")
	if nameNode == nil {
		return nil
	}

	annotationName := analyzer.GetNodeText(nameNode)
	position := analyzer.GetPosition(node)

	// 解析注解参数
	arguments := make(map[string]string)
	argsNode := analyzer.FindChildByType(node, "annotation_argument_list")
	if argsNode != nil {
		// 这里可以进一步解析注解参数，暂时简化处理
		argsText := analyzer.GetNodeText(argsNode)
		if argsText != "" {
			arguments["args"] = argsText
		}
	}

	return &AnnotationInfo{
		Name:      annotationName,
		Arguments: arguments,
		Position:  position,
	}
}

// AnalyzeJavaCode 综合分析Java代码
func (analyzer *JavaTreeSitterAnalyzer) AnalyzeJavaCode(code string) (*JavaAnalysisResult, error) {
	tree, err := analyzer.ParseCode(code)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Java code: %w", err)
	}
	// 注意：smacker/go-tree-sitter 不需要显式关闭Tree

	analyzer.LogParsingStats(tree, "Java")

	// 提取各种信息
	methodCalls, _ := analyzer.ExtractMethodCalls(tree)
	classes, _ := analyzer.ExtractClasses(tree)
	methods, _ := analyzer.ExtractMethods(tree)
	imports, _ := analyzer.ExtractImports(tree)
	annotations, _ := analyzer.ExtractAnnotations(tree)

	// 检测问题
	nPlusOneIssues, _ := analyzer.DetectNPlusOneQueries(tree)
	springAnnotations, _ := analyzer.DetectSpringAnnotations(tree)
	sqlInjectionRisks, _ := analyzer.DetectSQLInjectionRisks(tree)

	result := &JavaAnalysisResult{
		MethodCalls:       methodCalls,
		Classes:           classes,
		Methods:           methods,
		Imports:           imports,
		Annotations:       annotations,
		NPlusOneIssues:    nPlusOneIssues,
		SpringAnnotations: springAnnotations,
		SQLInjectionRisks: sqlInjectionRisks,
	}

	logger.Infof("Java代码分析完成: 方法调用%d个, 类%d个, 方法%d个, 导入%d个, 注解%d个",
		len(methodCalls), len(classes), len(methods), len(imports), len(annotations))

	return result, nil
}

// JavaAnalysisResult Java代码分析结果
type JavaAnalysisResult struct {
	MethodCalls       []MethodCall     `json:"method_calls"`
	Classes           []ClassInfo      `json:"classes"`
	Methods           []MethodInfo     `json:"methods"`
	Imports           []ImportInfo     `json:"imports"`
	Annotations       []AnnotationInfo `json:"annotations"`
	NPlusOneIssues    []string         `json:"n_plus_one_issues"`
	SpringAnnotations []string         `json:"spring_annotations"`
	SQLInjectionRisks []string         `json:"sql_injection_risks"`
}
