package service

import (
	"fmt"
	"strings"

	"ai-codereview-service/pkg/logger"
)

// EnhancedJavaReviewer 增强的Java审查器，集成Tree-sitter分析
type EnhancedJavaReviewer struct {
	treeSitterAnalyzer *JavaTreeSitterAnalyzer
}

// NewEnhancedJavaReviewer 创建增强的Java审查器
func NewEnhancedJavaReviewer() *EnhancedJavaReviewer {
	return &EnhancedJavaReviewer{
		treeSitterAnalyzer: NewJavaTreeSitterAnalyzer(),
	}
}

// ReviewJavaCode 审查Java代码，结合Tree-sitter分析和传统规则检查
func (reviewer *EnhancedJavaReviewer) ReviewJavaCode(code string, fileName string) (*JavaCodeReviewResult, error) {
	logger.Infof("开始使用Tree-sitter对Java代码进行增强审查: %s", fileName)

	// 使用Tree-sitter进行深度分析
	analysisResult, err := reviewer.treeSitterAnalyzer.AnalyzeJavaCode(code)
	if err != nil {
		logger.Warnf("Tree-sitter分析失败，回退到传统方法: %v", err)
		return reviewer.fallbackReview(code, fileName)
	}

	// 构建审查结果
	result := &JavaCodeReviewResult{
		FileName:          fileName,
		TreeSitterResult:  analysisResult,
		CodeSmells:        []string{},
		PerformanceIssues: []string{},
		SecurityIssues:    []string{},
		BestPractices:     []string{},
		QualityScore:      85, // 基础分数
	}

	// 基于Tree-sitter分析结果进行具体审查
	reviewer.analyzeCodeSmells(result)
	reviewer.analyzePerformanceIssues(result)
	reviewer.analyzeSecurityIssues(result)
	reviewer.analyzeBestPractices(result)

	// 计算质量分数
	reviewer.calculateQualityScore(result)

	logger.Infof("Java代码审查完成: %s", fileName)

	return result, nil
}

// analyzeCodeSmells 分析代码异味
func (reviewer *EnhancedJavaReviewer) analyzeCodeSmells(result *JavaCodeReviewResult) {
	// 1. 检查长方法
	for _, method := range result.TreeSitterResult.Methods {
		if len(strings.Split(method.Body, "\n")) > 50 {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("长方法: %s (行数: %d)", method.Name, len(strings.Split(method.Body, "\n"))))
		}
	}

	// 2. 检查过多参数
	for _, method := range result.TreeSitterResult.Methods {
		if len(method.Parameters) > 5 {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("参数过多: %s (%d个参数)", method.Name, len(method.Parameters)))
		}
	}

	// 3. 智能检查重复的导入 - 修复硬编码问题
	importMap := make(map[string]int)
	staticImportMap := make(map[string]int)

	for _, imp := range result.TreeSitterResult.Imports {
		// 清理和规范化包路径
		cleanPackage := strings.TrimSpace(imp.Package)
		cleanPackage = strings.TrimSuffix(cleanPackage, ";")

		// 跳过空包名
		if cleanPackage == "" {
			continue
		}

		// 分别统计静态导入和普通导入
		if imp.IsStatic {
			staticImportMap[cleanPackage]++
		} else {
			importMap[cleanPackage]++
		}
	}

	// 检查普通导入的重复
	for pkg, count := range importMap {
		if count > 1 {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("重复导入: %s (出现%d次)", pkg, count))
		}
	}

	// 检查静态导入的重复
	for pkg, count := range staticImportMap {
		if count > 1 {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("重复静态导入: %s (出现%d次)", pkg, count))
		}
	}

	// 检查静态导入与普通导入的冲突
	for pkg := range staticImportMap {
		// 提取基础包名（去掉具体的类或方法）
		basePkg := pkg
		if lastDot := strings.LastIndex(pkg, "."); lastDot > 0 {
			basePkg = pkg[:lastDot]
		}

		// 检查是否与普通导入冲突
		if _, exists := importMap[basePkg]; exists {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("导入冲突: 静态导入 %s 与普通导入 %s 可能冲突", pkg, basePkg))
		}
	}

	// 4. 检查通配符导入
	for _, imp := range result.TreeSitterResult.Imports {
		if imp.IsWild {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("通配符导入: %s (建议使用具体类名)", imp.Package))
		}
	}
}

// analyzePerformanceIssues 分析性能问题
func (reviewer *EnhancedJavaReviewer) analyzePerformanceIssues(result *JavaCodeReviewResult) {
	// 1. N+1查询问题
	result.PerformanceIssues = append(result.PerformanceIssues, result.TreeSitterResult.NPlusOneIssues...)

	// 2. 检查字符串拼接问题
	for _, method := range result.TreeSitterResult.Methods {
		if strings.Contains(method.Body, "String") && strings.Contains(method.Body, "+") {
			// 简单检测：如果方法体中有循环且有字符串拼接
			if strings.Contains(method.Body, "for") || strings.Contains(method.Body, "while") {
				result.PerformanceIssues = append(result.PerformanceIssues,
					fmt.Sprintf("疑似字符串拼接性能问题: %s (建议使用StringBuilder)", method.Name))
			}
		}
	}

	// 3. 检查大对象创建
	for _, methodCall := range result.TreeSitterResult.MethodCalls {
		if strings.Contains(methodCall.Method, "new") &&
			(strings.Contains(methodCall.Method, "List") ||
				strings.Contains(methodCall.Method, "Map") ||
				strings.Contains(methodCall.Method, "Set")) {
			result.PerformanceIssues = append(result.PerformanceIssues,
				fmt.Sprintf("第%d行: 考虑预设集合初始容量", methodCall.LineNumber))
		}
	}
}

// analyzeSecurityIssues 分析安全问题
func (reviewer *EnhancedJavaReviewer) analyzeSecurityIssues(result *JavaCodeReviewResult) {
	// 1. SQL注入风险
	result.SecurityIssues = append(result.SecurityIssues, result.TreeSitterResult.SQLInjectionRisks...)

	// 2. 检查硬编码密码
	for _, method := range result.TreeSitterResult.Methods {
		body := strings.ToLower(method.Body)
		if strings.Contains(body, "password") && strings.Contains(body, "=") {
			if !strings.Contains(body, "getpassword") && !strings.Contains(body, "setpassword") {
				result.SecurityIssues = append(result.SecurityIssues,
					fmt.Sprintf("疑似硬编码密码: %s", method.Name))
			}
		}
	}

	// 3. 检查敏感信息日志
	for _, methodCall := range result.TreeSitterResult.MethodCalls {
		if strings.Contains(methodCall.Method, "log") ||
			strings.Contains(methodCall.Method, "print") {
			for _, arg := range methodCall.Arguments {
				argLower := strings.ToLower(arg)
				if strings.Contains(argLower, "password") ||
					strings.Contains(argLower, "token") ||
					strings.Contains(argLower, "secret") {
					result.SecurityIssues = append(result.SecurityIssues,
						fmt.Sprintf("第%d行: 疑似敏感信息写入日志", methodCall.LineNumber))
				}
			}
		}
	}
}

// analyzeBestPractices 分析最佳实践
func (reviewer *EnhancedJavaReviewer) analyzeBestPractices(result *JavaCodeReviewResult) {
	// 1. Spring框架最佳实践 - 修复硬编码问题
	hasService := false
	hasController := false
	hasRepository := false
	hasDubboService := false

	for _, annotation := range result.TreeSitterResult.SpringAnnotations {
		if strings.Contains(annotation, "@Service") {
			hasService = true
		}
		if strings.Contains(annotation, "@DubboService") {
			hasDubboService = true
		}
		if strings.Contains(annotation, "@Controller") || strings.Contains(annotation, "@RestController") {
			hasController = true
		}
		if strings.Contains(annotation, "@Repository") {
			hasRepository = true
		}
	}

	// 智能判断服务层架构建议
	if hasController && !hasService && !hasDubboService {
		result.BestPractices = append(result.BestPractices,
			"建议: Controller层应该依赖Service层，而不是直接操作数据")
	}

	if (hasService || hasDubboService) && !hasRepository {
		result.BestPractices = append(result.BestPractices,
			"建议: Service层应该依赖Repository层进行数据访问")
	}

	// 注解使用建议 - 不强制要求特定注解
	if hasService && hasDubboService {
		result.BestPractices = append(result.BestPractices,
			"建议: 避免同时使用@Service和@DubboService注解，根据实际需求选择合适的注解")
	}

	// 2. 检查异常处理
	hasExceptionHandling := false
	for _, method := range result.TreeSitterResult.Methods {
		if strings.Contains(method.Body, "try") ||
			strings.Contains(method.Body, "catch") ||
			strings.Contains(method.Body, "throws") {
			hasExceptionHandling = true
			break
		}
	}

	if len(result.TreeSitterResult.Methods) > 3 && !hasExceptionHandling {
		result.BestPractices = append(result.BestPractices,
			"建议: 添加适当的异常处理机制")
	}

	// 3. 检查注释覆盖率
	commentCount := 0
	for _, method := range result.TreeSitterResult.Methods {
		if strings.Contains(method.Body, "//") || strings.Contains(method.Body, "/*") {
			commentCount++
		}
	}

	if len(result.TreeSitterResult.Methods) > 0 {
		commentRatio := float64(commentCount) / float64(len(result.TreeSitterResult.Methods))
		if commentRatio < 0.3 {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("建议: 提高注释覆盖率 (当前: %.1f%%)", commentRatio*100))
		}
	}

	// 4. 检查命名规范
	for _, method := range result.TreeSitterResult.Methods {
		if !reviewer.isValidJavaMethodName(method.Name) {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("方法命名不规范: %s (应使用驼峰命名)", method.Name))
		}
	}

	for _, class := range result.TreeSitterResult.Classes {
		if !reviewer.isValidJavaClassName(class.Name) {
			result.BestPractices = append(result.BestPractices,
				fmt.Sprintf("类命名不规范: %s (应使用帕斯卡命名)", class.Name))
		}
	}
}

// calculateQualityScore 计算代码质量分数
func (reviewer *EnhancedJavaReviewer) calculateQualityScore(result *JavaCodeReviewResult) {
	score := 85 // 基础分数

	// 扣除代码异味分数
	score -= len(result.CodeSmells) * 3

	// 扣除性能问题分数
	score -= len(result.PerformanceIssues) * 5

	// 扣除安全问题分数
	score -= len(result.SecurityIssues) * 10

	// 扣除最佳实践违反分数
	score -= len(result.BestPractices) * 2

	// 确保分数在合理范围内
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	result.QualityScore = score
}

// fallbackReview 传统审查方法（当Tree-sitter失败时使用）
func (reviewer *EnhancedJavaReviewer) fallbackReview(code string, fileName string) (*JavaCodeReviewResult, error) {
	logger.Warnf("使用传统方法审查Java代码: %s", fileName)

	result := &JavaCodeReviewResult{
		FileName:          fileName,
		CodeSmells:        []string{},
		PerformanceIssues: []string{},
		SecurityIssues:    []string{},
		BestPractices:     []string{},
		QualityScore:      70, // 传统方法基础分数较低
	}

	lines := strings.Split(code, "\n")

	// 简单的正则表达式检查
	for i, line := range lines {
		lineNum := i + 1
		lineLower := strings.ToLower(strings.TrimSpace(line))

		// 检查SQL注入风险
		if strings.Contains(lineLower, "select") && strings.Contains(line, "+") {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("第%d行: 疑似SQL注入风险", lineNum))
		}

		// 检查硬编码密码
		if strings.Contains(lineLower, "password") && strings.Contains(line, "=") {
			result.SecurityIssues = append(result.SecurityIssues,
				fmt.Sprintf("第%d行: 疑似硬编码密码", lineNum))
		}

		// 检查长行
		if len(line) > 120 {
			result.CodeSmells = append(result.CodeSmells,
				fmt.Sprintf("第%d行: 代码行过长 (%d字符)", lineNum, len(line)))
		}
	}

	return result, nil
}

// 辅助方法
func (reviewer *EnhancedJavaReviewer) isValidJavaMethodName(name string) bool {
	if len(name) == 0 {
		return false
	}
	// Java方法名应该以小写字母开头，使用驼峰命名
	firstChar := name[0]
	return firstChar >= 'a' && firstChar <= 'z'
}

func (reviewer *EnhancedJavaReviewer) isValidJavaClassName(name string) bool {
	if len(name) == 0 {
		return false
	}
	// Java类名应该以大写字母开头，使用帕斯卡命名
	firstChar := name[0]
	return firstChar >= 'A' && firstChar <= 'Z'
}

// JavaCodeReviewResult Java代码审查结果
type JavaCodeReviewResult struct {
	FileName          string              `json:"file_name"`
	TreeSitterResult  *JavaAnalysisResult `json:"tree_sitter_result,omitempty"`
	CodeSmells        []string            `json:"code_smells"`
	PerformanceIssues []string            `json:"performance_issues"`
	SecurityIssues    []string            `json:"security_issues"`
	BestPractices     []string            `json:"best_practices"`
	QualityScore      int                 `json:"quality_score"`
}
