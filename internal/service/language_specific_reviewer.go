package service

import (
	"fmt"
	"regexp"
	"strings"

	"ai-codereview-service/internal/config"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

// LanguageSpecificReviewer 语言特定审查器
type LanguageSpecificReviewer struct {
	config                     *config.Config
	javaReviewer               *JavaDubboReviewer
	enhancedJavaReviewer       *EnhancedJavaReviewer       // Tree-sitter增强审查器
	enhancedGoReviewer         *EnhancedGoReviewer         // Go Tree-sitter增强审查器
	enhancedJavaScriptReviewer *EnhancedJavaScriptReviewer // JavaScript Tree-sitter增强审查器
}

// NewLanguageSpecificReviewer 创建语言特定审查器
func NewLanguageSpecificReviewer(cfg *config.Config) *LanguageSpecificReviewer {
	return &LanguageSpecificReviewer{
		config:                     cfg,
		javaReviewer:               NewJavaDubboReviewer(),
		enhancedJavaReviewer:       NewEnhancedJavaReviewer(),
		enhancedGoReviewer:         NewEnhancedGoReviewer(),
		enhancedJavaScriptReviewer: NewEnhancedJavaScriptReviewer(),
	}
}

// ReviewByLanguage 根据语言进行特定审查
func (lsr *LanguageSpecificReviewer) ReviewByLanguage(language string, changes []repository.GitLabChange, contextMap map[string]*ContextInfo) (string, error) {
	logger.Infof("开始%s语言特定审查", language)

	switch language {
	case "java":
		// 优先使用Tree-sitter增强审查器
		var enhancedResults []string

		for _, change := range changes {
			filePath := change.NewPath
			if filePath == "" {
				filePath = change.OldPath
			}

			// 只处理Java文件
			if !strings.HasSuffix(filePath, ".java") {
				continue
			}

			context, exists := contextMap[filePath]
			if !exists {
				continue
			}

			// 使用Tree-sitter增强审查器
			javaResult, err := lsr.enhancedJavaReviewer.ReviewJavaCode(context.FileContent, filePath)
			if err != nil {
				logger.Warnf("Tree-sitter Java审查失败: %s, %v", filePath, err)
				continue
			}

			// 格式化Tree-sitter审查结果
			// 移除质量分数显示，保留其他分析结果

			if len(javaResult.CodeSmells) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("🔍 %s 代码异味:", filePath))
				for _, smell := range javaResult.CodeSmells {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", smell))
				}
			}

			if len(javaResult.PerformanceIssues) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("⚡ %s 性能问题:", filePath))
				for _, issue := range javaResult.PerformanceIssues {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", issue))
				}
			}

			if len(javaResult.SecurityIssues) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("🔒 %s 安全问题:", filePath))
				for _, issue := range javaResult.SecurityIssues {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", issue))
				}
			}

			if len(javaResult.BestPractices) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("💡 %s 最佳实践建议:", filePath))
				for _, practice := range javaResult.BestPractices {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", practice))
				}
			}
		}

		// 结合传统Dubbo审查器的结果
		traditionalResult, err := lsr.javaReviewer.Review(changes, contextMap)
		if err != nil {
			logger.Warnf("传统Java审查失败: %v", err)
		}

		// 合并结果
		var combinedResults []string
		if len(enhancedResults) > 0 {
			combinedResults = append(combinedResults, "🚀 Tree-sitter增强分析结果:")
			combinedResults = append(combinedResults, enhancedResults...)
		}

		if traditionalResult != "" {
			combinedResults = append(combinedResults, "\n📋 业务规则检查:")
			combinedResults = append(combinedResults, traditionalResult)
		}

		if len(combinedResults) == 0 {
			return "", nil
		}

		return strings.Join(combinedResults, "\n"), nil

	case "go":
		// Go语言Tree-sitter增强审查
		var enhancedResults []string

		for _, change := range changes {
			filePath := change.NewPath
			if filePath == "" {
				filePath = change.OldPath
			}

			// 只处理Go文件
			if !strings.HasSuffix(filePath, ".go") {
				continue
			}

			context, exists := contextMap[filePath]
			if !exists {
				continue
			}

			// 使用Tree-sitter增强审查器
			goResult, err := lsr.enhancedGoReviewer.ReviewGoCode(context.FileContent, filePath)
			if err != nil {
				logger.Warnf("Tree-sitter Go审查失败: %s, %v", filePath, err)
				continue
			}

			// 格式化Tree-sitter审查结果
			if len(goResult.CodeSmells) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("🔍 %s 代码异味:", filePath))
				for _, smell := range goResult.CodeSmells {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", smell))
				}
			}

			if len(goResult.PerformanceIssues) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("⚡ %s 性能问题:", filePath))
				for _, issue := range goResult.PerformanceIssues {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", issue))
				}
			}

			if len(goResult.SecurityIssues) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("🔒 %s 安全问题:", filePath))
				for _, issue := range goResult.SecurityIssues {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", issue))
				}
			}

			if len(goResult.BestPractices) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("💡 %s 最佳实践建议:", filePath))
				for _, practice := range goResult.BestPractices {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", practice))
				}
			}

			// 显示质量分数
			if goResult.QualityScore > 0 {
				scoreEmoji := "⭐"
				if goResult.QualityScore >= 90 {
					scoreEmoji = "🌟"
				} else if goResult.QualityScore >= 70 {
					scoreEmoji = "⭐"
				} else {
					scoreEmoji = "⚠️"
				}
				enhancedResults = append(enhancedResults, fmt.Sprintf("%s %s 代码质量分数: %d/100", scoreEmoji, filePath, goResult.QualityScore))
			}
		}

		if len(enhancedResults) == 0 {
			return "", nil
		}

		var finalResults []string
		finalResults = append(finalResults, "🚀 Go语言Tree-sitter增强分析结果:")
		finalResults = append(finalResults, enhancedResults...)

		return strings.Join(finalResults, "\n"), nil

	case "javascript":
		// JavaScript语言Tree-sitter增强审查
		var enhancedResults []string

		for _, change := range changes {
			filePath := change.NewPath
			if filePath == "" {
				filePath = change.OldPath
			}

			// 只处理JavaScript/TypeScript文件
			if !strings.HasSuffix(filePath, ".js") && !strings.HasSuffix(filePath, ".jsx") &&
				!strings.HasSuffix(filePath, ".ts") && !strings.HasSuffix(filePath, ".tsx") {
				continue
			}

			context, exists := contextMap[filePath]
			if !exists {
				continue
			}

			// 使用Tree-sitter增强审查器
			jsResult, err := lsr.enhancedJavaScriptReviewer.ReviewJavaScriptCode(context.FileContent, filePath)
			if err != nil {
				logger.Warnf("Tree-sitter JavaScript审查失败: %s, %v", filePath, err)
				continue
			}

			// 格式化Tree-sitter审查结果
			if len(jsResult.CodeSmells) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("🔍 %s 代码异味:", filePath))
				for _, smell := range jsResult.CodeSmells {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", smell))
				}
			}

			if len(jsResult.PerformanceIssues) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("⚡ %s 性能问题:", filePath))
				for _, issue := range jsResult.PerformanceIssues {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", issue))
				}
			}

			if len(jsResult.SecurityIssues) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("🔒 %s 安全问题:", filePath))
				for _, issue := range jsResult.SecurityIssues {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", issue))
				}
			}

			if len(jsResult.BestPractices) > 0 {
				enhancedResults = append(enhancedResults, fmt.Sprintf("💡 %s 最佳实践建议:", filePath))
				for _, practice := range jsResult.BestPractices {
					enhancedResults = append(enhancedResults, fmt.Sprintf("  - %s", practice))
				}
			}

			// 显示质量分数
			if jsResult.QualityScore > 0 {
				scoreEmoji := "⭐"
				if jsResult.QualityScore >= 90 {
					scoreEmoji = "🌟"
				} else if jsResult.QualityScore >= 70 {
					scoreEmoji = "⭐"
				} else {
					scoreEmoji = "⚠️"
				}
				enhancedResults = append(enhancedResults, fmt.Sprintf("%s %s 代码质量分数: %d/100", scoreEmoji, filePath, jsResult.QualityScore))
			}

			logger.Infof("JavaScript Tree-sitter增强分析结果: 代码异味%d个, 性能问题%d个, 安全问题%d个, 最佳实践建议%d个, 质量分数%d",
				len(jsResult.CodeSmells), len(jsResult.PerformanceIssues), len(jsResult.SecurityIssues), len(jsResult.BestPractices), jsResult.QualityScore)
		}

		if len(enhancedResults) == 0 {
			return "", nil
		}

		var finalResults []string
		finalResults = append(finalResults, "🚀 JavaScript语言Tree-sitter增强分析结果:")
		finalResults = append(finalResults, enhancedResults...)

		return strings.Join(finalResults, "\n"), nil

	default:
		return "", nil // 不支持的语言返回空字符串
	}
}

// JavaDubboReviewer Java/Dubbo项目专用审查器
type JavaDubboReviewer struct{}

// NewJavaDubboReviewer 创建Java/Dubbo审查器
func NewJavaDubboReviewer() *JavaDubboReviewer {
	return &JavaDubboReviewer{}
}

// Review 执行Java/Dubbo项目审查
func (jdr *JavaDubboReviewer) Review(changes []repository.GitLabChange, contextMap map[string]*ContextInfo) (string, error) {
	var issues []string

	for _, change := range changes {
		filePath := change.NewPath
		if filePath == "" {
			filePath = change.OldPath
		}

		context, exists := contextMap[filePath]
		if !exists {
			continue
		}

		fileContent := context.FileContent

		// 根据文件类型和内容进行审查
		issues = append(issues, jdr.reviewJavaFile(fileContent, filePath)...)
	}

	if len(issues) == 0 {
		return "", nil
	}

	return jdr.formatIssues(issues), nil
}

// reviewJavaFile 审查Java文件
func (jdr *JavaDubboReviewer) reviewJavaFile(content, filePath string) []string {
	var issues []string

	// Controller层审查
	if strings.Contains(filePath, "Controller") {
		issues = append(issues, jdr.reviewController(content, filePath)...)
	}

	// Service层审查
	if strings.Contains(filePath, "ServiceImpl") || strings.Contains(filePath, "Service") {
		issues = append(issues, jdr.reviewService(content, filePath)...)
	}

	// Manager层审查
	if strings.Contains(filePath, "Manager") {
		issues = append(issues, jdr.reviewManager(content, filePath)...)
	}

	// POM文件审查
	if strings.Contains(filePath, "pom.xml") {
		issues = append(issues, jdr.reviewPom(content, filePath)...)
	}

	// MyBatis XML审查
	if strings.HasSuffix(filePath, ".xml") && !strings.Contains(filePath, "pom.xml") {
		issues = append(issues, jdr.reviewMyBatisXml(content, filePath)...)
	}

	// 通用Java文件审查
	if strings.HasSuffix(filePath, ".java") {
		issues = append(issues, jdr.reviewGenericJava(content, filePath)...)
	}

	return issues
}

// reviewController Controller层审查
func (jdr *JavaDubboReviewer) reviewController(content, filePath string) []string {
	var issues []string

	// 1. 检查注解位置：@CommonExecutor, @MobileAPI 应该在接口上
	if strings.Contains(filePath, "Impl") {
		if strings.Contains(content, "@CommonExecutor") || strings.Contains(content, "@MobileAPI") {
			issues = append(issues, fmt.Sprintf("❌ %s: @CommonExecutor, @MobileAPI 注解应该放在接口上，不要放在实现类上", filePath))
		}
	}

	// 2. 检查上下文使用：uid, appId 等参数必须从上下文获取
	if strings.Contains(content, "uid") && !strings.Contains(content, "MobileAPIContext.get") {
		issues = append(issues, fmt.Sprintf("⚠️ %s: uid等参数建议从MobileAPIContext.get()获取，而不是依赖传参", filePath))
	}

	// 3. 检查业务逻辑：Controller不应包含复杂业务逻辑
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		// 检查数据库操作
		if strings.Contains(line, ".select") || strings.Contains(line, ".insert") || strings.Contains(line, ".update") {
			issues = append(issues, fmt.Sprintf("❌ %s:%d: Controller不应直接进行数据库操作", filePath, i+1))
		}
	}

	// 4. 检查参数包装：接口参数必须包装成request类
	if !strings.Contains(content, "Request") && strings.Count(content, "public") > 1 {
		issues = append(issues, fmt.Sprintf("⚠️ %s: 接口参数建议包装成Request类", filePath))
	}

	return issues
}

// isDubboServiceCandidate 判断是否为Dubbo服务候选者 - 更加严格的判断
func (jdr *JavaDubboReviewer) isDubboServiceCandidate(content, filePath string) bool {
	// 基本条件：必须是ServiceImpl且实现了接口
	if !strings.Contains(filePath, "ServiceImpl") || !strings.Contains(content, "implements") {
		return false
	}

	// 强烈的Dubbo服务特征检查
	dubboIndicators := []string{
		"@DubboReference", // 引用了其他Dubbo服务
		"@Reference",      // 老版本Dubbo引用
		"dubbo",           // 导入包含dubbo
		"rpc",             // RPC相关
		"api",             // 通常Dubbo服务会实现API接口
	}

	// 检查是否有明显的Dubbo特征
	hasDubboFeatures := false
	contentLower := strings.ToLower(content)
	for _, indicator := range dubboIndicators {
		if strings.Contains(contentLower, indicator) {
			hasDubboFeatures = true
			break
		}
	}

	// 检查文件路径是否暗示这是对外API服务
	isApiService := strings.Contains(strings.ToLower(filePath), "api") ||
		strings.Contains(strings.ToLower(filePath), "gateway") ||
		strings.Contains(strings.ToLower(filePath), "rpc")

	// 只有同时满足以下条件才认为是Dubbo服务候选者：
	// 1. 有明显的Dubbo特征，或者
	// 2. 文件路径暗示这是API服务
	return hasDubboFeatures || isApiService
}

// isExplicitlySpringService 判断是否明确标识为Spring服务 - 更准确的判断
func (jdr *JavaDubboReviewer) isExplicitlySpringService(content string) bool {
	hasService := strings.Contains(content, "@Service")
	hasDubboService := strings.Contains(content, "@DubboService")

	// 如果已经有@DubboService，就不是纯Spring服务
	if hasDubboService {
		return false
	}

	// 检查是否有明显的Spring内部服务特征
	springInternalFeatures := []string{
		"@Transactional",     // 事务管理
		"@Component",         // Spring组件
		"ApplicationContext", // Spring上下文
		"@Autowired",         // Spring依赖注入
		"@Repository",        // 数据访问层
		"JdbcTemplate",       // Spring JDBC
		"RedisTemplate",      // Spring Redis
	}

	hasSpringFeatures := false
	for _, feature := range springInternalFeatures {
		if strings.Contains(content, feature) {
			hasSpringFeatures = true
			break
		}
	}

	// 检查是否是内部业务服务（不对外暴露）
	isInternalService := strings.Contains(content, "Manager") ||
		strings.Contains(content, "Repository") ||
		strings.Contains(content, "Mapper")

	return hasService && (hasSpringFeatures || isInternalService)
}

// reviewService Service层审查 - 完全重写，消除硬编码
func (jdr *JavaDubboReviewer) reviewService(content, filePath string) []string {
	var issues []string

	// 1. 智能检查@DubboService注解 - 彻底修复硬编码问题
	if strings.Contains(filePath, "ServiceImpl") {
		hasDubboService := strings.Contains(content, "@DubboService")
		hasService := strings.Contains(content, "@Service")

		// 只有当确实是Dubbo服务候选者时才提示
		isDubboCandidate := jdr.isDubboServiceCandidate(content, filePath)
		isSpringService := jdr.isExplicitlySpringService(content)

		if isDubboCandidate && !hasDubboService && !isSpringService {
			// 只有明确的Dubbo服务候选者才提示
			issues = append(issues, fmt.Sprintf("💡 %s: 检测到可能的Dubbo服务实现，如需对外暴露建议使用@DubboService注解", filePath))
		} else if hasService && hasDubboService {
			// 如果同时使用了两个注解，提示冲突
			issues = append(issues, fmt.Sprintf("⚠️ %s: 不建议同时使用@Service和@DubboService注解，请选择其一", filePath))
		}
		// 对于明确的Spring内部服务，不再提示任何注解问题
	}

	// 2. 检查异常处理：建议使用YppRunTimeException
	if strings.Contains(content, "throw new") && !strings.Contains(content, "YppRunTimeException") {
		if strings.Contains(content, "Exception") && !strings.Contains(content, "RuntimeException") {
			issues = append(issues, fmt.Sprintf("💡 %s: 建议使用YppRunTimeException替代checked异常", filePath))
		}
	}

	// 3. 检查返回类型：建议避免Response<Void>
	if strings.Contains(content, "Response<Void>") {
		issues = append(issues, fmt.Sprintf("💡 %s: 建议使用Response<Boolean>替代Response<Void>", filePath))
	}

	// 4. 检查返回值方式：建议使用异常而非boolean标识
	if (strings.Contains(content, "return false") || strings.Contains(content, "return true")) &&
		strings.Contains(content, "boolean") {
		issues = append(issues, fmt.Sprintf("💡 %s: 对于业务操作结果，建议使用YppRunTimeException而非boolean返回值", filePath))
	}

	return issues
}

// reviewManager Manager层审查
func (jdr *JavaDubboReviewer) reviewManager(content, filePath string) []string {
	var issues []string

	// 1. 检查N+1查询问题
	lines := strings.Split(content, "\n")
	inForLoop := false

	for i, line := range lines {
		line = strings.TrimSpace(line)

		if strings.Contains(line, "for (") || strings.Contains(line, "for(") {
			inForLoop = true
		}

		if inForLoop && strings.Contains(line, "}") {
			inForLoop = false
		}

		if inForLoop {
			// 检查数据库查询
			if strings.Contains(line, ".select") || strings.Contains(line, ".find") || strings.Contains(line, ".get") {
				issues = append(issues, fmt.Sprintf("⚠️ %s:%d: 疑似N+1查询问题，避免在循环中进行数据库查询", filePath, i+1))
			}

			// 检查RPC调用
			if strings.Contains(line, "Service") && strings.Contains(line, ".") {
				issues = append(issues, fmt.Sprintf("⚠️ %s:%d: 避免在循环中进行RPC调用", filePath, i+1))
			}
		}
	}

	// 2. 检查并行查询机会
	queryCount := strings.Count(content, ".select") + strings.Count(content, ".find") + strings.Count(content, ".get")
	if queryCount >= 3 {
		issues = append(issues, fmt.Sprintf("💡 %s: 发现%d个查询操作，建议考虑使用syncExecutor进行并行查询优化", filePath, queryCount))
	}

	return issues
}

// reviewPom POM文件审查
func (jdr *JavaDubboReviewer) reviewPom(content, filePath string) []string {
	var issues []string

	// 检查Dubbo版本
	dubboVersionRegex := regexp.MustCompile(`<dubbo\.version>(.*?)</dubbo\.version>`)
	matches := dubboVersionRegex.FindStringSubmatch(content)

	if len(matches) > 1 {
		version := matches[1]
		if !strings.HasPrefix(version, "2.7") {
			issues = append(issues, fmt.Sprintf("⚠️ %s: 建议使用Dubbo 2.7版本，当前版本: %s", filePath, version))
		}
	}

	return issues
}

// reviewMyBatisXml MyBatis XML文件审查
func (jdr *JavaDubboReviewer) reviewMyBatisXml(content, filePath string) []string {
	var issues []string

	// 1. 检查Base_Column_List
	if !strings.Contains(content, "Base_Column_List") {
		issues = append(issues, fmt.Sprintf("⚠️ %s: MyBatis映射文件建议定义Base_Column_List", filePath))
	}

	// 2. 检查空列表处理
	if strings.Contains(content, "foreach") && !strings.Contains(content, "choose") {
		issues = append(issues, fmt.Sprintf("⚠️ %s: 使用foreach时建议添加空列表检查，使用choose-when-otherwise结构", filePath))
	}

	return issues
}

// reviewGenericJava 通用Java文件审查
func (jdr *JavaDubboReviewer) reviewGenericJava(content, filePath string) []string {
	var issues []string

	// 1. 检查Lombok使用
	if (strings.Contains(content, "public get") || strings.Contains(content, "public set")) &&
		!strings.Contains(content, "@Data") {
		issues = append(issues, fmt.Sprintf("💡 %s: 建议使用Lombok @Data注解替代手动的getter/setter", filePath))
	}

	// 2. 检查日志使用规范
	if strings.Contains(content, "log.info") && strings.Contains(content, "uid=") {
		issues = append(issues, fmt.Sprintf("⚠️ %s: 日志打印建议使用 uid: {} 而不是 uid={}", filePath))
	}

	return issues
}

// formatIssues 格式化问题列表
func (jdr *JavaDubboReviewer) formatIssues(issues []string) string {
	if len(issues) == 0 {
		return ""
	}

	var result strings.Builder
	result.WriteString("### 🔍 Java/Dubbo项目审查结果\n\n")

	errorCount := 0
	warningCount := 0
	suggestionCount := 0

	for _, issue := range issues {
		result.WriteString("- ")
		result.WriteString(issue)
		result.WriteString("\n")

		if strings.Contains(issue, "❌") {
			errorCount++
		} else if strings.Contains(issue, "⚠️") {
			warningCount++
		} else if strings.Contains(issue, "💡") {
			suggestionCount++
		}
	}

	result.WriteString(fmt.Sprintf("\n**统计**: 错误 %d 个，警告 %d 个，建议 %d 个\n", errorCount, warningCount, suggestionCount))

	return result.String()
}
