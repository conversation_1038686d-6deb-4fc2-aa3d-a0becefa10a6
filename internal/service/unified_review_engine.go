package service

import (
	"fmt"
	"strings"
	"time"

	"ai-codereview-service/internal/config"
	"ai-codereview-service/internal/gitlab"
	"ai-codereview-service/internal/llm"
	"ai-codereview-service/internal/repository"
	"ai-codereview-service/pkg/logger"
)

// UnifiedReviewEngine 统一的审查引擎
// 整合手动和自动化审查逻辑，默认都采用增强审查
type UnifiedReviewEngine struct {
	config                      *config.Config
	llmClient                   *llm.Client
	enhancedReviewer            *EnhancedCodeReviewer
	contextExtractor            *ContextExtractor
	recursiveDependencyAnalyzer *RecursiveDependencyAnalyzer
	languageSpecificReviewer    *LanguageSpecificReviewer
}

// ReviewRequest 统一的审查请求
type ReviewRequest struct {
	ProjectName  string                    `json:"project_name"`
	ProjectID    string                    `json:"project_id"`
	Changes      []repository.GitLabChange `json:"changes"`
	Commits      interface{}               `json:"commits"` // 可以是 []repository.Commit 或 []repository.GitLabCommitInfo
	GitlabClient *gitlab.Client            `json:"-"`
	Ref          string                    `json:"ref"`
	BranchName   string                    `json:"branch_name"`
	ReviewType   ReviewType                `json:"review_type"`
	SourceBranch string                    `json:"source_branch,omitempty"`
	TargetBranch string                    `json:"target_branch,omitempty"`
}

// ReviewType 审查类型
type ReviewType string

const (
	ReviewTypeEnhanced ReviewType = "enhanced"
	ReviewTypeBasic    ReviewType = "basic"
)

const (
	ReviewTypePush         ReviewType = "push"
	ReviewTypeMergeRequest ReviewType = "merge_request"
	ReviewTypeManual       ReviewType = "manual"
	ReviewTypeCommit       ReviewType = "commit"
)

// ReviewResult 审查结果
type ReviewResult struct {
	Result        string              `json:"result"`
	FileCount     int                 `json:"file_count"`
	ContextFiles  []string            `json:"context_files"`
	Dependencies  map[string][]string `json:"dependencies"`
	Language      string              `json:"language"`
	ReviewMetrics *ReviewMetrics      `json:"metrics"`
}

// ReviewMetrics 审查指标
type ReviewMetrics struct {
	ProcessingTime   time.Duration `json:"processing_time"`
	TokensUsed       int           `json:"tokens_used"`
	ContextDepth     int           `json:"context_depth"`
	DependencyLayers int           `json:"dependency_layers"`
	FilesAnalyzed    int           `json:"files_analyzed"`
}

// NewUnifiedReviewEngine 创建统一审查引擎
func NewUnifiedReviewEngine(cfg *config.Config, llmClient *llm.Client) *UnifiedReviewEngine {
	return &UnifiedReviewEngine{
		config:    cfg,
		llmClient: llmClient,
		// 其他组件将在第一次使用时延迟初始化
	}
}

// ReviewCode 统一的代码审查入口点
// 这是唯一的公共审查方法，所有审查请求都通过这里处理
func (ure *UnifiedReviewEngine) ReviewCode(request *ReviewRequest) (*ReviewResult, error) {
	startTime := time.Now()
	logger.Infof("开始统一代码审查: 项目=%s, 类型=%s, 文件数=%d",
		request.ProjectName, request.ReviewType, len(request.Changes))

	// 初始化审查组件
	if err := ure.initializeComponents(request.GitlabClient); err != nil {
		return nil, fmt.Errorf("初始化审查组件失败: %w", err)
	}

	// 检测主要编程语言
	primaryLanguage := ure.detectPrimaryLanguage(request.Changes)
	logger.Infof("检测到主要编程语言: %s", primaryLanguage)

	// 执行增强审查（默认启用，充分利用128k上下文）
	result, err := ure.performEnhancedReview(request, primaryLanguage)
	if err != nil {
		logger.Warnf("增强审查失败，尝试基础审查: %v", err)
		result, err = ure.performBasicReview(request, primaryLanguage)
		if err != nil {
			return nil, fmt.Errorf("代码审查失败: %w", err)
		}
	}

	// 计算审查指标
	contextDepth := 0
	if result.ContextFiles != nil {
		contextDepth = len(result.ContextFiles)
	}

	metrics := &ReviewMetrics{
		ProcessingTime:   time.Since(startTime),
		TokensUsed:       llm.CountTokens(result.Result),
		ContextDepth:     contextDepth,
		DependencyLayers: ure.calculateDependencyDepth(result.Dependencies),
		FilesAnalyzed:    result.FileCount,
	}
	result.ReviewMetrics = metrics

	logger.Infof("统一代码审查完成: 耗时=%v, Tokens=%d, 上下文文件=%d",
		metrics.ProcessingTime, metrics.TokensUsed, metrics.ContextDepth)

	return result, nil
}

// performEnhancedReview 执行增强审查
func (ure *UnifiedReviewEngine) performEnhancedReview(request *ReviewRequest, primaryLanguage string) (*ReviewResult, error) {
	logger.Info("执行增强审查 - 启用多级依赖分析和全量上下文注入")

	// 1. 多级依赖递归分析（2-3层深度）
	dependencies, err := ure.recursiveDependencyAnalyzer.AnalyzeDependencies(
		request.Changes, request.Ref, request.BranchName, 3) // 最大3层深度
	if err != nil {
		logger.Warnf("依赖分析失败: %v", err)
		dependencies = make(map[string][]string)
	}

	// 2. 收集全量上下文（充分利用128k容量）
	contextMap := ure.enhancedReviewer.GatherFullContextWithDependencies(
		request.Changes, request.Ref, request.BranchName, dependencies)

	// 3. 语言特定审查
	languageReview, err := ure.languageSpecificReviewer.ReviewByLanguage(
		primaryLanguage, request.Changes, contextMap)
	if err != nil {
		logger.Warnf("语言特定审查失败: %v", err)
		languageReview = ""
	}

	// 4. 构建审查内容
	changesText := ure.buildChangesText(request.Changes)
	commitsText := ure.buildCommitsText(request.Commits)

	// 5. 执行增强审查
	reviewText, err := ure.enhancedReviewer.ReviewWithFullContext(
		changesText, commitsText, contextMap, languageReview, primaryLanguage)
	if err != nil {
		return nil, err
	}

	// 6. 构建结果
	result := &ReviewResult{
		Result:       ure.stripMarkdown(reviewText),
		FileCount:    len(contextMap),
		ContextFiles: ure.extractContextFileList(contextMap),
		Dependencies: dependencies,
		Language:     primaryLanguage,
	}

	return result, nil
}

// performBasicReview 执行基础审查（作为后备方案）
func (ure *UnifiedReviewEngine) performBasicReview(request *ReviewRequest, primaryLanguage string) (*ReviewResult, error) {
	logger.Info("执行基础审查模式")

	changesText := ure.buildChangesText(request.Changes)
	commitsText := ure.buildCommitsText(request.Commits)

	// 基础审查逻辑
	systemPrompt := ure.buildSystemPrompt(primaryLanguage)
	userPrompt := ure.buildUserPrompt(changesText, commitsText, primaryLanguage)

	messages := []llm.Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := ure.llmClient.ChatCompletion(messages)
	if err != nil {
		return nil, fmt.Errorf("LLM调用失败: %w", err)
	}

	result := &ReviewResult{
		Result:    ure.stripMarkdown(response),
		FileCount: len(request.Changes),
		Language:  primaryLanguage,
	}

	return result, nil
}

// initializeComponents 初始化审查组件
func (ure *UnifiedReviewEngine) initializeComponents(gitlabClient *gitlab.Client) error {
	// 🔧 **修复：每次都重新创建组件，避免GitLab客户端状态保持问题**
	// 问题原因：之前的逻辑会复用组件，导致不同项目之间的GitLab客户端混乱

	// 使用更大的token限制，充分利用128k上下文
	maxTokens := ure.config.Review.MaxTokens
	if maxTokens < 100000 {
		maxTokens = 100000 // 最小10万tokens
	}

	// 每次都重新创建，确保使用正确的GitLab客户端
	ure.enhancedReviewer = NewEnhancedCodeReviewer(gitlabClient, ure.llmClient, maxTokens)
	ure.contextExtractor = NewContextExtractor(gitlabClient)
	ure.recursiveDependencyAnalyzer = NewRecursiveDependencyAnalyzer(gitlabClient, ure.contextExtractor)

	// 语言特定审查器不依赖GitLab客户端，可以复用
	if ure.languageSpecificReviewer == nil {
		ure.languageSpecificReviewer = NewLanguageSpecificReviewer(ure.config)
	}

	return nil
}

// detectPrimaryLanguage 检测主要编程语言
func (ure *UnifiedReviewEngine) detectPrimaryLanguage(changes []repository.GitLabChange) string {
	languageCount := make(map[string]int)

	for _, change := range changes {
		var fileName string
		if change.NewPath != "" {
			fileName = change.NewPath
		} else if change.OldPath != "" {
			fileName = change.OldPath
		}

		if fileName == "" {
			continue
		}

		// 检测文件扩展名
		if strings.HasSuffix(fileName, ".java") {
			languageCount["java"]++
		} else if strings.HasSuffix(fileName, ".go") {
			languageCount["go"]++
		} else if strings.HasSuffix(fileName, ".py") {
			languageCount["python"]++
		} else if strings.HasSuffix(fileName, ".js") || strings.HasSuffix(fileName, ".ts") {
			languageCount["javascript"]++
		} else if strings.HasSuffix(fileName, ".xml") && strings.Contains(fileName, "pom.xml") {
			languageCount["java"]++ // pom.xml 是Java项目的标志
		}
	}

	// 返回出现最多的语言
	maxCount := 0
	primaryLanguage := "unknown"
	for lang, count := range languageCount {
		if count > maxCount {
			maxCount = count
			primaryLanguage = lang
		}
	}

	return primaryLanguage
}

// 辅助方法
func (ure *UnifiedReviewEngine) buildChangesText(changes []repository.GitLabChange) string {
	var builder strings.Builder
	for _, change := range changes {
		builder.WriteString(fmt.Sprintf("File: %s\n", change.NewPath))
		builder.WriteString(change.Diff)
		builder.WriteString("\n\n")
	}
	return builder.String()
}

func (ure *UnifiedReviewEngine) buildCommitsText(commits interface{}) string {
	switch c := commits.(type) {
	case []repository.Commit:
		var messages []string
		for _, commit := range c {
			messages = append(messages, strings.TrimSpace(commit.Message))
		}
		return strings.Join(messages, "; ")
	case []repository.GitLabCommitInfo:
		var messages []string
		for _, commit := range c {
			messages = append(messages, strings.TrimSpace(commit.Title))
		}
		return strings.Join(messages, "; ")
	default:
		return ""
	}
}

func (ure *UnifiedReviewEngine) buildSystemPrompt(primaryLanguage string) string {
	basePrompt := `你是一位资深的软件开发工程师，专注于代码的业务逻辑正确性。本次任务是对员工的代码进行审查。`

	// 根据语言添加特定的审查要求
	switch primaryLanguage {
	case "java":
		basePrompt += `

### Java/Dubbo 项目特定审查要点：
1. **Controller层规范**: 检查@CommonExecutor和@MobileAPI注解使用是否正确
2. **业务逻辑分层**: 确保Controller不包含业务逻辑，只做参数透传
3. **异常处理**: 建议使用YppRunTimeException，避免返回true/false标识
4. **性能检查**: 避免N+1查询问题，检查循环中的数据库操作
5. **服务注解**: 根据实际需求选择合适的服务注解（@Service或@DubboService）
6. **依赖注入**: 检查@Resource注解使用和依赖管理
7. **安全检查**: 验证从MobileAPIContext获取用户信息而非依赖传参`
	case "go":
		basePrompt += `

### Go 项目特定审查要点：
1. **错误处理**: 检查error返回值是否正确处理
2. **并发安全**: 检查goroutine和channel使用
3. **内存管理**: 检查slice和map的使用是否会导致内存泄漏
4. **接口设计**: 检查接口定义和实现是否合理`
	}

	return basePrompt
}

func (ure *UnifiedReviewEngine) buildUserPrompt(changesText, commitsText, primaryLanguage string) string {
	return fmt.Sprintf(`请审查以下%s代码变更：

代码变更内容：
%s

提交信息：
%s

请重点关注业务逻辑正确性、潜在bug和最佳实践合规性。`, primaryLanguage, changesText, commitsText)
}

func (ure *UnifiedReviewEngine) stripMarkdown(text string) string {
	text = strings.TrimSpace(text)
	if strings.HasPrefix(text, "```markdown") && strings.HasSuffix(text, "```") {
		text = strings.TrimPrefix(text, "```markdown")
		text = strings.TrimSuffix(text, "```")
	}
	return strings.TrimSpace(text)
}

func (ure *UnifiedReviewEngine) calculateDependencyDepth(dependencies map[string][]string) int {
	maxDepth := 0
	for _, deps := range dependencies {
		if len(deps) > maxDepth {
			maxDepth = len(deps)
		}
	}
	return maxDepth
}
func (ure *UnifiedReviewEngine) extractContextFileList(contextMap map[string]*ContextInfo) []string {
	files := make([]string, 0, len(contextMap))
	for filePath := range contextMap {
		files = append(files, filePath)
	}
	return files
}
