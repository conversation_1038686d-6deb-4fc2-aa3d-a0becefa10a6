# API 路径说明

## 统一后的API路径结构

现在所有API都统一到 `/api/v1` 路径下，使用RESTful风格的路径设计。

### 新的API路径表

| 功能分类 | 新路径 | 方法 | 原路径 | 说明 |
|---------|--------|------|--------|------|
| **GitLab Webhook** | `/api/v1/webhook` | POST | `/webhook` | GitLab webhook接收 |
| **代码审查** | `/api/v1/review/manual` | POST | `/manual-review` | 手动代码审查 |
| **系统状态** | `/api/v1/system/health` | GET | `/health` | 健康检查 |
| **系统状态** | `/api/v1/system/stats` | GET | `/stats` | 系统统计 |
| **钉钉配置** | `/api/v1/dingtalk-configs` | POST | - | 创建钉钉配置 |
| **钉钉配置** | `/api/v1/dingtalk-configs` | GET | - | 获取钉钉配置列表 |
| **钉钉配置** | `/api/v1/dingtalk-configs/:id` | GET | - | 获取单个钉钉配置 |
| **钉钉配置** | `/api/v1/dingtalk-configs/:id` | PUT | - | 更新钉钉配置 |
| **钉钉配置** | `/api/v1/dingtalk-configs/:id` | DELETE | - | 删除钉钉配置 |

## 详细API说明

### 1. GitLab Webhook
```
POST /api/v1/webhook
Content-Type: application/json

接收GitLab发送的webhook事件
```

### 2. 代码审查API

#### 手动代码审查
```
POST /api/v1/review/manual
Content-Type: application/json

{
  "project_id": 123,
  "source_branch": "feature-branch",
  "target_branch": "master"
}
```

支持多种格式：
- 基于项目ID和分支比较
- 基于项目名称和分支比较  
- 基于项目名称和commit范围
- 基于单个commit

### 3. 系统状态API

#### 健康检查
```
GET /api/v1/system/health

响应:
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 系统统计
```
GET /api/v1/system/stats

响应:
{
  "push_reviews": 42,
  "mr_reviews": 15,
  "date": "2024-01-01"
}
```

### 4. 钉钉配置管理API

**注意**：钉钉配置现在使用token字段而不是完整的webhook URL。系统会自动将token拼接为：
`https://oapi.dingtalk.com/robot/send?access_token={token}`

#### 创建配置
```
POST /api/v1/dingtalk-configs
Content-Type: application/json

{
  "project_name": "my-project",
  "branch": "master",
  "event_type": "push",
  "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
}
```

#### 获取配置列表
```
GET /api/v1/dingtalk-configs
GET /api/v1/dingtalk-configs?project_name=my-project
GET /api/v1/dingtalk-configs?branch=master
GET /api/v1/dingtalk-configs?event_type=push
```

#### 获取单个配置
```
GET /api/v1/dingtalk-configs/1
```

#### 更新配置
```
PUT /api/v1/dingtalk-configs/1
Content-Type: application/json

{
  "token": "new_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
}
```

#### 删除配置
```
DELETE /api/v1/dingtalk-configs/1
```
## 迁移建议

### 1. GitLab Webhook配置更新

**标准配置：**
```
http://your-server:5001/api/v1/webhook
```

### 2. 客户端调用更新

**标准调用方式：**
```bash
curl -X POST http://localhost:5001/api/v1/review/manual \
  -H "Content-Type: application/json" \
  -d '{"project_id": 123, "source_branch": "dev", "target_branch": "master"}'
```

### 3. 监控和健康检查更新

**标准调用方式：**
```bash
curl http://localhost:5001/api/v1/system/health
curl http://localhost:5001/api/v1/system/stats
```

### 4. 钉钉配置管理

**创建配置：**
```bash
curl -X POST http://localhost:5001/api/v1/dingtalk-configs \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "my-project",
    "branch": "master",
    "event_type": "push",
    "token": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
  }'
```

## API版本管理

- **当前版本**：`v1`
- **版本策略**：主要功能变更会增加版本号
- **兼容性**：v1 API将长期维护，确保向后兼容

## 统一路径的优势

1. **一致性**：所有API遵循统一的路径规范
2. **版本管理**：通过 `/api/v1` 进行版本控制
3. **组织性**：按功能模块组织路径结构
4. **扩展性**：便于后续添加新的API功能
5. **标准化**：符合RESTful API设计规范 